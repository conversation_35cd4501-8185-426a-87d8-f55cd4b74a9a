<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.derbysoft.next</groupId>
        <artifactId>next-parent</artifactId>
        <version>3.4.1.M1-SNAPSHOT</version>
    </parent>
    <packaging>war</packaging>
    <artifactId>next-propertyconnect-msc</artifactId>
    <version>1.1.0</version>

    <name>Next PropertyConnect MSC</name>
    <description>Next PropertyConnect MSC</description>
    <repositories>
        <repository>
            <id>atomic-public</id>
            <url>https://repo.dbaws.net/repository/atomic-public</url>
        </repository>
    </repositories>

    <properties>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.compilerVersion>17</maven.compiler.compilerVersion>
        <maven.compiler.release>17</maven.compiler.release>
    </properties>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <activeProperties>local</activeProperties>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <activeProperties>uat</activeProperties>
            </properties>
        </profile>
        <profile>
            <id>qa</id>
            <properties>
                <activeProperties>qa</activeProperties>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <activeProperties>prod</activeProperties>
            </properties>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
<!--            <dependency>-->
<!--                <groupId>com.squareup.okhttp3</groupId>-->
<!--                <artifactId>okhttp</artifactId>-->
<!--                <version>4.11.0</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.yaml</groupId>-->
<!--                <artifactId>snakeyaml</artifactId>-->
<!--                <version>2.0</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>com.fasterxml.woodstox</groupId>-->
<!--                <artifactId>woodstox-core</artifactId>-->
<!--                <version>6.5.1</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>3.6.1</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.derbysoft.next</groupId>-->
<!--                <artifactId>next-commons-jdbc</artifactId>-->
<!--                <version>2.6.3</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.15.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.derbysoft.next</groupId>
            <artifactId>next-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.derbysoft.next</groupId>
            <artifactId>next-starter-mongo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.derbysoft.next</groupId>
            <artifactId>next-starter-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.derbysoft.next</groupId>
            <artifactId>next-starter-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.derbysoft.next</groupId>
            <artifactId>next-commons-feign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.derbysoft.next</groupId>
            <artifactId>next-commons-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.derbysoft.next</groupId>
            <artifactId>next-commons-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.derbysoft.next</groupId>
            <artifactId>next-commons-sla</artifactId>
        </dependency>
        <dependency>
            <groupId>com.derbysoft.next</groupId>
            <artifactId>next-commons-util</artifactId>
        </dependency>

        <dependency>
            <groupId>com.derbysoft.next</groupId>
            <artifactId>next-extension</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>ses</artifactId>
            <version>2.21.40</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sts</artifactId>
            <version>2.21.40</version>
        </dependency>

        <dependency>
            <groupId>software.amazon.jdbc</groupId>
            <artifactId>aws-advanced-jdbc-wrapper</artifactId>
<!--            <version>2.5.4</version>-->
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.derbysoft.next</groupId>
            <artifactId>next-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <type>pom</type>
            <scope>compile</scope>
            <exclusions>
                <exclusion> <!-- exclude TestNG because it confuses Surefire's auto-detection -->
                    <groupId>org.apache.groovy</groupId>
                    <artifactId>groovy-testng</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <build>
        <finalName>pcmsc</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>${git-commit-id-plugin.version}</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <phase>initialize</phase>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <!-- 检查的仓库根目录，${project.basedir}：项目根目录，即包含pom.xml文件的目录 -->
                    <dotGitDirectory>${project.basedir}/.git</dotGitDirectory>
                    <!-- false：扫描路径时不打印更多信息，默认值false，可以不配置 -->
                    <verbose>false</verbose>
                    <!-- 定义插件中所有时间格式，默认值：yyyy-MM-dd’T’HH:mm:ssZ -->
                    <dateFormat>yyyy-MM-dd HH:mm:ss</dateFormat>
                    <!-- git属性文件中各属性前缀，默认值git，可以不配置 -->
                    <prefix>git</prefix>
                    <!-- 生成git属性文件，默认false：不生成 -->
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <!-- 生成git属性文件路径及文件名，默认${project.build.outputDirectory}/git.properties -->
                    <generateGitPropertiesFilename>${project.build.outputDirectory}/git.properties
                    </generateGitPropertiesFilename>
                    <!-- 生成git属性文件格式，默认值properties -->
                    <format>json</format>
                    <!-- 配置git-describe命令 -->
                    <gitDescribe>
                        <skip>false</skip>
                        <always>false</always>
                        <dirty>-dirty</dirty>
                    </gitDescribe>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <release>${maven.compiler.release}</release>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>com.derbysoft.next</groupId>
                            <artifactId>next-extension</artifactId>
                            <version>${next.extension.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
