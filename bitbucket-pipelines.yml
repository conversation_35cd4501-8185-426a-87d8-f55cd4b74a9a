image: image-center.dbaws.net/build/build-amazon-linux:20230717

pipelines:
  branches:
    develop:
      - parallel:
          - step:
              name: pcmsc-uat-build
              runs-on:
                - self.hosted
                - linux
              script:
                - pipe: docker://image-center.dbaws.net/base/derbysoft-ade:latest
                  variables:
                    ACTION: "build"
                    BUILD_PLAN_ID: "64994959c8718d27e273e9cc"
                    ARTIFACT_NAME: "build_msg_uat.json"
              artifacts:
                - build_msg_uat.json
              services:
                - docker
          - step:
              name: pcmsc-rt-build
              runs-on:
                - self.hosted
                - linux
              script:
                - pipe: docker://image-center.dbaws.net/base/derbysoft-ade:latest
                  variables:
                    ACTION: "build"
                    BUILD_PLAN_ID: "66a0b64cc8718d57bf5925ff"
                    ARTIFACT_NAME: "build_msg_rt.json"
              artifacts:
                - build_msg_rt.json
              services:
                - docker
          - step:
              name: pcmsc-qa-build
              runs-on:
                - self.hosted
                - linux
              script:
                - pipe: docker://image-center.dbaws.net/base/derbysoft-ade:latest
                  variables:
                    ACTION: "build"
                    BUILD_PLAN_ID: "658d3867c8718d2d050e7313"
                    ARTIFACT_NAME: "build_msg_qa.json"
              artifacts:
                - build_msg_qa.json
              services:
                - docker
      - parallel:
          - step:
              name: pcmsc-uat-deployment-update-apply
              runs-on:
                - self.hosted
                - linux
              script:
                - pipe: docker://image-center.dbaws.net/base/derbysoft-ade:latest
                  variables:
                    ACTION: "update_and_apply"
                    ADE_DEPLOYMENT_NAME: "pcmsc-k8s-uat-8vc81x5n6"
                    ARTIFACTS_LIST: "build_msg_uat.json"
                    CONTAINER_VERSIONS: "derby-tomcat9:9.0.91-11.0.23-20240902-ef15d"
                    POD_NUMBER: "1"
              services:
                - docker
          - step:
              name: pcmsc-rt-deployment-update-apply
              runs-on:
                - self.hosted
                - linux
              script:
                - pipe: docker://image-center.dbaws.net/base/derbysoft-ade:latest
                  variables:
                    ACTION: "update_and_apply"
                    ADE_DEPLOYMENT_NAME: "pcmsc-k8s-rt-x493hmsgj"
                    ARTIFACTS_LIST: "build_msg_rt.json"
                    CONTAINER_VERSIONS: "derby-tomcat9:9.0.91-11.0.23-20240902-ef15d"
                    POD_NUMBER: "1"
              services:
                - docker
          - step:
              name: pcmsc-qa-deployment-update-apply
              runs-on:
                - self.hosted
                - linux
              script:
                - pipe: docker://image-center.dbaws.net/base/derbysoft-ade:latest
                  variables:
                    ACTION: "update_and_apply"
                    ADE_DEPLOYMENT_NAME: "pcmsc-qa-k8s-86s32drt2"
                    ARTIFACTS_LIST: "build_msg_qa.json"
                    CONTAINER_VERSIONS: "derby-tomcat9:9.0.100-11.0.26-20250228-6d046"
                    POD_NUMBER: "1"
              services:
                - docker
