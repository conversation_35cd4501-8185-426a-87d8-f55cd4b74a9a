package com.derbysoft.next.sample.pms.openapi

import com.derbysoft.next.commons.core.exception.ServiceException
import com.derbysoft.next.commons.core.http.TransactionType
import com.derbysoft.next.commons.core.http.UrlWrapper
import com.derbysoft.next.commons.util.compress.CompressUtil
import com.derbysoft.next.sample.client.PcCMFeignClient
import com.derbysoft.next.sample.domain.entity.MessageRecordEntity
import com.derbysoft.next.sample.dto.AccessTokenConfigDTO
import com.derbysoft.next.sample.dto.JWTInfo
import com.derbysoft.next.sample.dto.profile.HotelSystemChannelInfo
import com.derbysoft.next.sample.factory.OkHttp3HttpClientFactory
import com.derbysoft.next.sample.util.CommonDateUtils
import com.derbysoft.next.sample.util.CommonUtils
import com.derbysoft.next.sample.util.Constants
import groovy.json.JsonSlurper
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.exception.ExceptionUtils
import org.joda.time.DateTime
import org.joda.time.DateTimeZone

import java.text.MessageFormat
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

def record = properties.get("record") as MessageRecordEntity
def cmClient = properties.get("cmClient") as PcCMFeignClient
def clientFactory = properties.get("clientFactory") as OkHttp3HttpClientFactory

def okHttpClient = clientFactory.get(Constants.CONTENT_TYPE_VALUE_JSON)

try {
    OpenApiMessage message = new OpenApiMessage()
    message.header = new Head()
    message.header.echoToken = UUID.randomUUID().toString()
    message.header.timeStamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"))
    message.header.version = 'V1'
    message.hotelId = record.hotelSystemHotelId
    message.distributorId = record.channelId
    message.thread = new Thread()
    message.thread.threadId = record.threadId
    message.thread.businessType = record.businessType
    message.thread.extensions = new JsonSlurper().parseText(record.extensionsJson)
    message.thread.message = new Message()
    message.thread.message.messageId = record.messageId
    message.thread.message.messageType = record.messageType
    message.thread.message.message = record.messageContent?.message
    message.thread.message.translatedMessage = record.messageContent?.translatedMessage
    message.thread.message.imageUrl = record.messageContent?.imageUrl
    message.thread.message.createdTime =  new DateTime(record.messageCreatedDate, DateTimeZone.UTC).toString(CommonDateUtils.DATETIME_PATTERN_UTC)
    message.thread.message.extensions = record.messageContent?.extensions
    if (record.reservationDetail) {
        message.thread.reservation = new ReservationDetail()
        message.thread.reservation.distributorResId = record.reservationDetail.channelResId
        message.thread.reservation.checkIn = record.reservationDetail.checkIn
        message.thread.reservation.checkOut = record.reservationDetail.checkOut
        message.thread.reservation.totalAmount = record.reservationDetail.totalAmount
        message.thread.reservation.adultCount = record.reservationDetail.adultCount
        message.thread.reservation.childCount = record.reservationDetail.childCount
        message.thread.reservation.roomId = record.reservationDetail.channelRoomId
        message.thread.reservation.rateId = record.reservationDetail.channelRateId
        message.thread.reservation.extensions = record.reservationDetail.extensions
    }
    HotelSystemChannelInfo hotelDetail = new JsonSlurper().parseText(CompressUtil.decompressStr(record.profile))
    def systemConnection = hotelDetail.getHotelSystemConnectionDTOs().find(e -> e.hotelSystemConnectionId == record.hotelSystemConnectionId)
    def endpoint = Optional.ofNullable(systemConnection.settings).map(e -> CommonUtils.toString(e.get('messageUrl'))).orElse('')
    if (StringUtils.isEmpty(endpoint)) {
        throw new ServiceException("InvalidEndpoint", MessageFormat.format("connection invalid endpoint. connectionId {0}", record.hotelSystemConnectionId));
    }
    AccessTokenConfigDTO accessToken = cmClient.generateAccessToken(new JWTInfo(hotelSystemConnectionId: record.getHotelSystemConnectionId()))
    def authToken = accessToken.authToken
    def header = ["Authorization": "Bearer ${authToken}".toString()]
    try {
        def res = okHttpClient.post(new UrlWrapper(TransactionType.Legacy.name(), 'openapi.msgSend', "${endpoint}/message/send"),
                message, OpenApiMessageResponse.class, header)
        if (Objects.equals(res.success, true.toString())) {
            record.status = MessageRecordEntity.Status.SUCCESS
        } else {
            record.status = MessageRecordEntity.Status.FAILED
            record.errorMessage = 'response is not success'
        }
    } catch (Exception e) {
        throw new ServiceException("HotelSystemErr_ExternalError", e.message)
    }
} catch (ServiceException e) {
    record.status = MessageRecordEntity.Status.FAILED
    record.errorMessage = e.errorMessage
} catch (Exception e) {
    record.status = MessageRecordEntity.Status.FAILED
    record.errorMessage = ExceptionUtils.getStackTrace(e)
}

class OpenApiMessageResponse {
    Head header
    String success
}

class OpenApiMessage {
    Head header
    String hotelId
    String distributorId
    Thread thread
}

class Head {
    String echoToken
    String timeStamp
    String version
}

class Message {
    String messageId
    String messageType
    String message
    String translatedMessage
    String imageUrl
    String createdTime
    Map<String, Object> extensions
}

class Thread {
    String threadId
    String businessType
    Message message
    ReservationDetail reservation
    Map<String, Object> extensions
}

class ReservationDetail {
    String distributorResId
    String checkIn
    String checkOut
    String totalAmount
    String adultCount
    String childCount
    String roomId
    String rateId
    Map<String, Object> extensions
}