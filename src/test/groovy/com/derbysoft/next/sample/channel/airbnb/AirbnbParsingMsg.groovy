package com.derbysoft.next.sample.channel.airbnb

import com.derbysoft.next.commons.core.exception.ServiceException
import com.derbysoft.next.commons.util.compress.CompressUtil
import com.derbysoft.next.sample.client.PcProfileFeignClient
import com.derbysoft.next.sample.domain.entity.MessageRecordEntity
import com.derbysoft.next.sample.dto.MessageContent
import com.derbysoft.next.sample.dto.ReservationDetail
import com.derbysoft.next.sample.util.CommonDateUtils
import com.derbysoft.next.sample.util.CommonUtils
import com.derbysoft.next.sample.util.Constants
import com.google.gson.Gson
import groovy.json.JsonSlurper
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.exception.ExceptionUtils
import org.assertj.core.util.Lists
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.text.MessageFormat

def logger = LoggerFactory.getLogger("AirbnbParsingMsg") as Logger

def msgContent = properties.get("msgContent") as String
def channelId = properties.get("channelId") as String
def profileClient = properties.get("profileClient") as PcProfileFeignClient
def record = properties.get("record") as MessageRecordEntity
def gson = properties.get("gson") as Gson

try {
    AirbnbWebhookMessage airbnbMsg = new JsonSlurper().parseText(msgContent)
    def threadId = airbnbMsg?.thread?.id
    def messageId = airbnbMsg?.message?.id
    record.channelId = channelId
    record.messageSource = MessageRecordEntity.MessageSource.CHANNEL
    record.threadId = threadId
    record.messageId = messageId
    if (airbnbMsg?.message?.created_at) {
        record.messageCreatedDate = CommonDateUtils.parseDate(airbnbMsg?.message?.created_at, CommonDateUtils.DATETIME_PATTERN_UTC)
    }
    def isImage = Optional.ofNullable(airbnbMsg?.message?.attachment_images)
            .map(images ->
                    images.stream()
                            .anyMatch(e ->
                                    Objects.nonNull(e.url)))
            .orElse(false)
    record.messageType = isImage ? MessageRecordEntity.MessageType.IMAGE : MessageRecordEntity.MessageType.TEXT
    def messageContent = MessageContent.builder().build()
    if (isImage) {
        messageContent.imageUrl = airbnbMsg?.message?.attachment_images.stream().filter(e -> Objects.nonNull(e.url)).findFirst().get().url
    } else {
        messageContent.message = airbnbMsg?.message?.message
        messageContent.translatedMessage = airbnbMsg?.message?.translated_message
    }
    messageContent.extensions = [:]
    messageContent.extensions.userId = airbnbMsg?.message?.user_id
    def attachment = airbnbMsg?.thread?.attachment
    def roles = attachment?.roles
    def users = airbnbMsg?.thread?.users
    messageContent.extensions.userName = users.stream()
            .filter(e -> e.id == messageContent.extensions.userId)
            .findFirst()
            .map(e -> e.first_name)
            .orElse('')
    record.messageContent = messageContent
    record.businessType = MessageRecordEntity.BusinessType.OTHER
    record.status = MessageRecordEntity.Status.IGNORE
    def businessPurpose = Optional.ofNullable(airbnbMsg?.thread?.business_purpose).orElse('')
    switch (businessPurpose) {
        case 'booking_direct_thread':
            record.businessType = MessageRecordEntity.BusinessType.RESERVATION
            record.status = MessageRecordEntity.Status.PENDING
            break
        case 'support_messaging_thread':
            record.businessType = MessageRecordEntity.BusinessType.SUPPORT
            break
    }
    def extensions = [:]
    extensions.channelStatus = attachment.status
    extensions.channelType = attachment.type
    if (roles) {
        extensions.roles = roles
    }
    if (users) {
        extensions.users = users
    }
    record.extensionsJson = gson.toJson(extensions)
    def bookingDetails = attachment?.booking_details
    def listingId = bookingDetails?.listing_id
    def isGuest = false
    if (StringUtils.isNotEmpty(listingId)) {
        if (bookingDetails) {
            def resDetail = ReservationDetail.builder().build()
            resDetail.checkIn = bookingDetails?.checkin_date
            resDetail.checkOut = bookingDetails?.checkout_date
            resDetail.totalAmount = bookingDetails?.expected_payout_amount_accurate
            resDetail.adultCount = bookingDetails?.number_of_adults
            resDetail.childCount = bookingDetails?.number_of_children
            resDetail.channelRoomId = listingId
            resDetail.channelResId = bookingDetails?.reservation_confirmation_code
            resDetail.extensions = [:]
            resDetail.extensions.guestCount = bookingDetails?.number_of_guests
            resDetail.extensions.infantCount = bookingDetails?.number_of_infants
            resDetail.extensions.petCount = bookingDetails?.number_of_pets
            resDetail.extensions.days = bookingDetails?.nights
            resDetail.extensions.listingId = listingId
            resDetail.extensions.listingName = bookingDetails?.listing_name
            record.reservationDetail = resDetail
        }
        def userId = airbnbMsg?.message?.user_id
        isGuest = Optional.ofNullable(roles)
                .map(e -> {
                    def role = e.stream().filter(f ->
                            Optional.ofNullable(f.user_ids)
                                    .orElse(Lists.newArrayList()).contains(userId))
                            .findFirst()
                            .map(item ->
                                    item.role)
                            .orElse('')
                    return StringUtils.equalsIgnoreCase(role, 'guest')
                }).orElse(false)
        def hotelDetail = profileClient.findAirbnbHotelDetailByListingId(record.channelId, listingId)
        if (Objects.nonNull(hotelDetail) && Objects.nonNull(hotelDetail.hotelId)) {
            record.profile = CompressUtil.compressStr(gson.toJson(hotelDetail))
            record.channelHotelId = hotelDetail.hotelConnectionDTO?.channelHotelId
            record.hotelId = hotelDetail.hotelId
            var hotelSystemSetting = Optional.ofNullable(hotelDetail.hotelSystemInstanceDTO?.hotelSystemSettings)
                    .map(settings -> {
                        var optional = settings.stream().filter(e -> e.connectorType == 'MESSAGE').findFirst()
                        if (optional.isPresent()) {
                            return optional.get()
                        }
                        optional = settings.stream().filter(e -> e.connectorType == 'ALL').findFirst()
                        if (optional.isPresent()) {
                            return optional.get()
                        }
                        throw new ServiceException('HotelSystemSettingNotFound',
                                MessageFormat.format('hotel system setting not found. hotelId {0} connectorType: MESSAGE', hotelDetail.hotelId))
                    })
                    .orElseThrow(() -> new ServiceException('HotelSystemSettingNotFound',
                            MessageFormat.format('hotel system setting not found. hotelId {0} connectorType: MESSAGE', hotelDetail.hotelId)))

            record.hotelSystemConnectionId = hotelSystemSetting.hotelSystemConnectionId
            record.hotelSystemChainId = hotelSystemSetting.hotelSystemChainId
            record.hotelSystemHotelId = hotelSystemSetting.hotelSystemHotelId
            var systemConnection = Optional.ofNullable(hotelDetail.getHotelSystemConnectionDTOs())
                    .orElse(Lists.newArrayList())
                    .stream()
                    .filter(e -> e.hotelSystemConnectionId == record.hotelSystemConnectionId)
                    .findFirst()
                    .orElseThrow(() -> new ServiceException('HotelSystemConnectionNotFound',
                            MessageFormat.format('hotel system connection not found. connectionId {0}', record.hotelSystemConnectionId)))

            record.hotelSystemId = systemConnection.hotelSystemId
            record.apiType = systemConnection.apiType
            if ('OPENAPI' != record.apiType || !isReceiveMessage(hotelSystemSetting, systemConnection)) {
                record.status = MessageRecordEntity.Status.IGNORE
            }
            record.hotelSystemVer = systemConnection.hotelSystemVer
            record.scriptVer = Optional.ofNullable(systemConnection.settings)
                    .map(e -> CommonUtils.toString(e[Constants.ScriptVersion.MSG_SEND_VER]))
                    .orElse(Constants.ScriptVersion.DEFAULT_VERSION)

            var roomId = Optional.ofNullable(hotelDetail.hotelChannelMappingDTO?.productMapping)
                    .map(mappings ->
                            mappings.stream().filter(e -> e.channelRoomId == listingId).findFirst()
                    ).filter(e -> e.isPresent())
                    .map(e -> e.get().roomId)
                    .orElse(listingId)
            record.reservationDetail.roomId = roomId
            var hotelSystemRoomId = Optional.ofNullable(hotelDetail.hotelSystemMappingDTOs?.productMapping)
                    .map(mappings ->
                            mappings.stream().filter(e -> e.roomId == roomId).findFirst()
                    ).filter(e -> e.isPresent())
                    .map(e -> e.get().roomId)
                    .orElse(roomId)
            record.reservationDetail.hotelSystemRoomId = hotelSystemRoomId
        } else {
            record.status = MessageRecordEntity.Status.IGNORE
        }
    }
    if (!isGuest || StringUtils.isEmpty(listingId)) {
        record.status = MessageRecordEntity.Status.IGNORE
    }
} catch (ServiceException e) {
    record.status = MessageRecordEntity.Status.FAILED
    record.errorMessage = e.errorMessage
} catch (Exception e) {
    record.status = MessageRecordEntity.Status.FAILED
    record.errorMessage = ExceptionUtils.getStackTrace(e)
}

static boolean isReceiveMessage(def hotelSystemSetting, def systemConnection) {
    return Optional.ofNullable(hotelSystemSetting.setting)
            .map(e ->
                    Boolean.valueOf(CommonUtils.toString(e.get('receiveMsg'))))
            .orElse(Optional.ofNullable(systemConnection.settings)
                    .map(e ->
                            Boolean.valueOf(CommonUtils.toString(e.get('receiveMsg'))))
                    .orElse(false))
}

class AirbnbWebhookMessage {
    String action
    Message message
    Thread thread
}

class Message {
    String id
    String message
    String translated_message
    String user_id
    String created_at
    List<Image> attachment_images
}

class Image {
    String url
}

class Thread {
    String id
    String business_purpose
    String updated_at
    Attachment attachment
    List<User> users
}

class Attachment {
    String type
    String status
    BookingDetail booking_details
    List<Role> roles
}

class BookingDetail {
    String checkin_date
    String checkout_date
    String expected_payout_amount_accurate
    String listing_id
    String listing_name
    String nights
    String non_response_at
    String number_of_guests
    String number_of_adults
    String number_of_children
    String number_of_infants
    String reservation_confirmation_code
    String number_of_pets
}

class Role {
    String role
    List<String> user_ids
}

class User {
    String id
    String first_name
    String location
    String preferred_locale
}
