package com.derbysoft.next.sample.dto

import spock.lang.Specification

class EmailRetryRequestTest extends Specification {

    EmailRetryRequest request

    def setup() {
        request = new EmailRetryRequest()
    }

    def "Test setting and getting messageId"() {
        given:
        def messageId = "123"

        when:
        request.setMessageId(messageId)

        then:
        request.getMessageId() == messageId
    }

    def "Test setting and getting messageType"() {
        given:
        def messageType = "email"

        when:
        request.setMessageType(messageType)

        then:
        request.getMessageType() == messageType
    }

    def "Test setting and getting kafkaMgmDomain"() {
        given:
        def kafkaMgmDomain = "test.domain"

        when:
        request.setKafkaMgmDomain(kafkaMgmDomain)

        then:
        request.getKafkaMgmDomain() == kafkaMgmDomain
    }

    def "Test setting and getting kafkaMgmClusterId"() {
        given:
        def kafkaMgmClusterId = "1234"

        when:
        request.setKafkaMgmClusterId(kafkaMgmClusterId)

        then:
        request.getKafkaMgmClusterId() == kafkaMgmClusterId
    }

}
