package com.derbysoft.next.sample.dto

import spock.lang.Specification

class SendRawEmailRequestTest extends Specification {

    def "test SendRawEmailRequest object creation"() {
        given:
        def messageId = "123"
        def mailFrom = "<EMAIL>"
        def mailTo = ["<EMAIL>", "<EMAIL>"]
        def mailCc = ["<EMAIL>", "<EMAIL>"]
        def subject = "Test email"
        def content = "This is a test email content."

        when:
        def sendRawEmailRequest = new SendRawEmailRequest(
                mailFrom: mailFrom,
                mailTo: mailTo as Set,
                mailCc: mailCc as Set,
                subject: subject,
                content: content
        )
        sendRawEmailRequest.setMessageId(messageId)

        then:
        sendRawEmailRequest.getMessageId() == messageId
        sendRawEmailRequest.mailFrom == mailFrom
        sendRawEmailRequest.mailTo == (mailTo as Set)
        sendRawEmailRequest.mailCc == (mailCc as Set)
        sendRawEmailRequest.subject == subject
        sendRawEmailRequest.content == content
    }


}
