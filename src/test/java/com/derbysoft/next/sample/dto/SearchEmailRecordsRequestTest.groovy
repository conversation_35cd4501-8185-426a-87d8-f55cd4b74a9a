package com.derbysoft.next.sample.dto

import spock.lang.Specification

class SearchEmailRecordsRequestTest extends Specification {

    SearchEmailRecordsRequest request

    def setup() {
        request = new SearchEmailRecordsRequest()
    }

    def "Test setting and getting page"() {
        given:
        def page = 1

        when:
        request.setPage(page)

        then:
        request.getPage() == page
    }

    def "Test setting and getting size"() {
        given:
        def size = 10

        when:
        request.setSize(size)

        then:
        request.getSize() == size
    }

    def "Test setting and getting messageId"() {
        given:
        def messageId = "123"

        when:
        request.setMessageId(messageId)

        then:
        request.getMessageId() == messageId
    }

    def "Test setting and getting messageType"() {
        given:
        def messageType = "email"

        when:
        request.setMessageType(messageType)

        then:
        request.getMessageType() == messageType
    }

    def "Test setting and getting mailFrom"() {
        given:
        def mailFrom = "<EMAIL>"

        when:
        request.setMailFrom(mailFrom)

        then:
        request.getMailFrom() == mailFrom
    }

    def "Test setting and getting mailTo"() {
        given:
        def mailTo = new HashSet()
        mailTo.add("<EMAIL>")
        mailTo.add("<EMAIL>")

        when:
        request.setMailTo(mailTo)

        then:
        request.getMailTo() == mailTo
    }

    def "Test setting and getting mailCc"() {
        given:
        def mailCc = new HashSet()
        mailCc.add("<EMAIL>")
        mailCc.add("<EMAIL>")

        when:
        request.setMailCc(mailCc)

        then:
        request.getMailCc() == mailCc
    }

    def "Test setting and getting subject"() {
        given:
        def subject = "Test Subject"

        when:
        request.setSubject(subject)

        then:
        request.getSubject() == subject
    }

    def "Test setting and getting content"() {
        given:
        def content = "Test Content"

        when:
        request.setContent(content)

        then:
        request.getContent() == content
    }

    def "Test setting and getting template"() {
        given:
        def template = "Test Template"

        when:
        request.setTemplate(template)

        then:
        request.getTemplate() == template
    }

    def "Test setting and getting templateData"() {
        given:
        def templateData = ["key1": "value1", "key2": "value2"]

        when:
        request.setTemplateData(templateData)

        then:
        request.getTemplateData() == templateData
    }

    def "Test setting and getting supplier"() {
        given:
        def supplier = "Test Supplier"

        when:
        request.setSupplier(supplier)

        then:
        request.getSupplier() == supplier
    }

    def "Test setting and getting status"() {
        given:
        def status = "Test Status"

        when:
        request.setStatus(status)

        then:
        request.getStatus() == status
    }


}
