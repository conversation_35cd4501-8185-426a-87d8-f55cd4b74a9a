package com.derbysoft.next.sample.dto

import spock.lang.Specification

class SendTemplatedEmailRequestTest extends Specification {

    SendTemplatedEmailRequest request

    def setup() {
        request = new SendTemplatedEmailRequest()
    }

    def "Test setting and getting messageId"() {
        given:
        def messageId = "123"

        when:
        request.setMessageId(messageId)

        then:
        request.getMessageId() == messageId
    }

    def "Test setting and getting mailFrom"() {
        given:
        def mailFrom = "<EMAIL>"

        when:
        request.setMailFrom(mailFrom)

        then:
        request.getMailFrom() == mailFrom
    }

    def "Test setting and getting mailTo"() {
        given:
        def mailTo = ["<EMAIL>", "<EMAIL>"] as Set

        when:
        request.setMailTo(mailTo)

        then:
        request.getMailTo() == mailTo
    }

    def "Test setting and getting mailCc"() {
        given:
        def mailCc = ["<EMAIL>", "<EMAIL>"] as Set

        when:
        request.setMailCc(mailCc)

        then:
        request.getMailCc() == mailCc
    }

    def "Test setting and getting subject"() {
        given:
        def subject = "Test Subject"

        when:
        request.setSubject(subject)

        then:
        request.getSubject() == subject
    }

    def "Test setting and getting template"() {
        given:
        def template = "test_template"

        when:
        request.setTemplate(template)

        then:
        request.getTemplate() == template
    }

    def "Test setting and getting templateData"() {
        given:
        def templateData = [key1: "value1", key2: "value2"]

        when:
        request.setTemplateData(templateData)

        then:
        request.getTemplateData() == templateData
    }

}
