package com.derbysoft.next.sample.dto

import spock.lang.Specification

class KafkaConsumeResponseTest extends Specification {

    KafkaConsumeResponse response

    def setup() {
        response = new KafkaConsumeResponse()
    }

    def "Test setting and getting topic"() {
        given:
        def topic = "test"

        when:
        response.setTopic(topic)

        then:
        response.getTopic() == topic
    }

    def "Test setting and getting partition"() {
        given:
        def partition = 1

        when:
        response.setPartition(partition)

        then:
        response.getPartition() == partition
    }

    def "Test setting and getting offset"() {
        given:
        def offset = 123

        when:
        response.setOffset(offset)

        then:
        response.getOffset() == offset
    }

    def "Test setting and getting timestamp"() {
        given:
        def timestamp = 1621946983000L

        when:
        response.setTimestamp(timestamp)

        then:
        response.getTimestamp() == timestamp
    }

    def "Test setting and getting key"() {
        given:
        def key = "test-key"

        when:
        response.setKey(key)

        then:
        response.getKey() == key
    }

    def "Test setting and getting value"() {
        given:
        def value = "test-value"

        when:
        response.setValue(value)

        then:
        response.getValue() == value
    }


}
