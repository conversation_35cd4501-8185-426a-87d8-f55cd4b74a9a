package com.derbysoft.next.sample.dto

import spock.lang.Specification

class SendEmailMessageTest extends Specification {

    SendEmailMessage message

    def setup() {
        message = SendEmailMessage.builder().build()
    }

    def "Test setting and getting messageId"() {
        given:
        def messageId = "123"

        when:
        message.setMessageId(messageId)

        then:
        message.getMessageId() == messageId
    }

    def "Test setting and getting mailFrom"() {
        given:
        def mailFrom = "<EMAIL>"

        when:
        message.setMailFrom(mailFrom)

        then:
        message.getMailFrom() == mailFrom
    }

    def "Test setting and getting mailTo"() {
        given:
        def mailTo = new String[]{"<EMAIL>", "<EMAIL>"}

        when:
        message.setMailTo(mailTo)

        then:
        message.getMailTo() == mailTo
    }

    def "Test setting and getting mailCc"() {
        given:
        def mailCc = new String[]{"<EMAIL>", "<EMAIL>"}

        when:
        message.setMailCc(mailCc)

        then:
        message.getMailCc() == mailCc
    }

    def "Test setting and getting mailSubject"() {
        given:
        def mailSubject = "Test Subject"

        when:
        message.setMailSubject(mailSubject)

        then:
        message.getMailSubject() == mailSubject
    }

    def "Test setting and getting mailContent"() {
        given:
        def mailContent = "Test Content"

        when:
        message.setMailContent(mailContent)

        then:
        message.getMailContent() == mailContent
    }


}
