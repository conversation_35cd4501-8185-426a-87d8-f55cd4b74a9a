package com.derbysoft.next.sample.mail

import org.assertj.core.util.Sets
import spock.lang.Specification

class MailGroupHelperTest extends Specification {

    def mailGroupHelper = new MailGroupHelper()
    def mailMaxCount = 2

    def setup() {
        mailGroupHelper.mailMaxCount = mailMaxCount
    }

    def 'Test no split'() {
        given:
        def mailGroups = []
        def mailTo = Sets.newHashSet(['test1.qq@com'])
        def mailCc = Sets.newHashSet(['test2.qq@com'])

        when:
        mailGroups = mailGroupHelper.groups(mailTo, mailCc)

        then:
        mailGroups.size() == 1
        mailGroups[0].mailTo[0] == 'test1.qq@com'
        mailGroups[0].mailCc[0] == 'test2.qq@com'
    }

    def 'Test split mailTo size more than maxCc size'() {
        given:
        def mailGroups = []
        def mailTo = Sets.newHashSet(['test1.qq@com', 'test2.qq@com', 'test3.qq@com'])
        def mailCc = Sets.newHashSet(['test4.qq@com', 'test5.qq@com'])

        when:
        mailGroups = mailGroupHelper.groups(mailTo, mailCc)

        then:
        mailGroups.size() == 3
        mailGroups[0].mailTo.size() == 1
        mailGroups[1].mailTo.size() == 1
        mailGroups[2].mailTo.size() == 1
        mailGroups[0].mailCc.size() == 1
        mailGroups[1].mailCc.size() == 1
        mailGroups[2].mailCc == null
    }

    def 'Test split mailTo size equals maxCc size'() {
        given:
        def mailGroups = []
        def mailTo = Sets.newHashSet(['test1.qq@com', 'test2.qq@com'])
        def mailCc = Sets.newHashSet(['test3.qq@com', 'test4.qq@com'])

        when:
        mailGroups = mailGroupHelper.groups(mailTo, mailCc)

        then:
        mailGroups.size() == 2
        mailGroups[0].mailTo.size() == 1
        mailGroups[1].mailTo.size() == 1
        mailGroups[0].mailCc.size() == 1
        mailGroups[1].mailCc.size() == 1
    }

    def 'Test split mailTo size less than maxCc size'() {
        given:
        def mailGroups = []
        def mailTo = Sets.newHashSet(['test1.qq@com'])
        def mailCc = Sets.newHashSet(['test2.qq@com', 'test3.qq@com', 'test4.qq@com', 'test5.qq@com'])

        when:
        mailGroups = mailGroupHelper.groups(mailTo, mailCc)

        then:
        mailGroups.size() == 4
        mailGroups[0].mailTo.size() == 1
        mailGroups[1].mailTo.size() == 1
        mailGroups[2].mailTo.size() == 1
        mailGroups[3].mailTo.size() == 1
        mailGroups[0].mailCc.size() == 1
        mailGroups[1].mailCc == null
        mailGroups[2].mailCc == null
        mailGroups[3].mailCc == null
    }
}
