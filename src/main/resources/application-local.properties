server.port=9998

configkeeper.url=service-registry-cn.derbysoft-test.com:10080
configkeeper.source=pcmsc:cn-northwest-1:next:::
service.group=local

## log
logger.ROOT=INFO
logger.perf=INFO
logger.action=INFO
logger.http.AccessLog =INFO
logger.http.StreamLog=INFO

log.file.ROOT.path = /usr/local/logs/pcmsc.log
log.file.perf.path = /usr/local/logs/pcmsc.perf_v2.log
log.file.action.path = /usr/local/logs/pcmsc.action.log
log.file.http.AccessLog.path = /usr/local/logs/pcmsc.http.access.log
log.file.http.StreamLog.path = /usr/local/logs/pcmsc.streamindex_v2.log

log.appender.ROOT = console
log.appender.perf = console
log.appender.action = console
log.appender.http.AccessLog = console
log.appender.http.StreamLog = console


#credential.manager.app.name=pcmsc
#credential.manager.host= https://credential-manager.derbysoft-test.com
#credential.manager.source=AWSIAM

#sql
#spring.datasource.url=***********************************************************************************************************************************************************************************************************
#spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

spring.datasource.url=jdbc:aws-wrapper:mysql://operaconnector.cluster-c3siuwdqpqse.rds.cn-northwest-1.amazonaws.com.cn/uat_propertyconnect_msc?useUnicode=true&characterEncoding=UTF-8&characterEncoding=utf8&autoReconnect=true&useSSL=false
spring.datasource.driver-class-name=software.amazon.jdbc.Driver

#spring.datasource.url=jdbc-credentialmanager:mysql://operaconnector.cluster-c3siuwdqpqse.rds.cn-northwest-1.amazonaws.com.cn:3306/uat_propertyconnect_msc?useUnicode=true&useSSL=false&characterEncoding=UTF-8&cmServiceId=f38831668ae242e38bd8974f94cab730&cmAccessToken=ddf88aa07f7e479ca0f77d352d3424be
#spring.datasource.driver-class-name=com.derbysoft.credentialmanager.driver.CredentialManagerMySQLDriver

spring.datasource.hikari.minimum-idle=20
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.connection-timeout=30000

#spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
#spring.jpa.properties.hibernate.format_sql=false
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect

## idWorker
idWorker.nodeId=1

## eureka
spring.application.name=pcmsc
eureka.client.serviceUrl.defaultZone=http://127.0.0.1:7000/pceureka/eureka/
eureka.instance.lease-renewal-interval-in-seconds= 5
eureka.instance.lease-expiration-duration-in-seconds=15
eureka.client.registry-fetch-interval-seconds=5
eureka.instance.prefer-ip-address=true
eureka.client.enabled=false
spring.cloud.loadbalancer.cache.ttl=2s

## ses smtp
ses.mail.smtp.priority=1
ses.mail.smtp.domain-identity=@aws-ses.derbysoft.com
ses.mail.smtp.host=email-smtp.us-west-2.amazonaws.com
ses.mail.smtp.port=587

### spring mail (gmail sendCloud)
spring.mail.priority=2
spring.mail.domain-identity=@derbysoft.net
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true

## ses api
ses.mail.api.priority=3
ses.mail.api.region=ap-southeast-1
#ses.mail.api.domain-identity="pc.noreply" <<EMAIL>>
#ses.mail.api.sourceArn=aws.ses-arn=arn:aws:ses:us-west-2:815886405294:identity/notification.qa.derbysoft-test.com

## mongo
spring.data.mongodb.uri=mongodb://*************:27017/pcmsc
spring.data.mongodb.auto-index-creation=true

# kafka
spring.kafka.bootstrapServers = pcnxuatkafka01.derbysoft-test.com:9092
spring.kafka.producer.valueSerializer = org.apache.kafka.common.serialization.ByteArraySerializer
spring.kafka.producer.acks=all

spring.kafka.consumer.bootstrapServers = pcnxuatkafka01.derbysoft-test.com:9092
spring.kafka.consumer.groupId = pc_msc_group_local
spring.kafka.consumer.autoOffsetReset = earliest
spring.kafka.consumer.valueDeserializer = org.apache.kafka.common.serialization.ByteArrayDeserializer
spring.kafka.consumer.enableAutoCommit = false
spring.kafka.consumer.maxPollRecords=1
spring.kafka.consumer.properties.heartbeat.interval.ms = 10000
spring.kafka.consumer.properties.session.timeout.ms = 30000
spring.kafka.consumer.properties.max.poll.interval.ms = 3600000
spring.kafka.listener.ackMode = MANUAL
spring.kafka.listener.concurrency = 4
spring.kafka.listener.pollTimeout = 2000

next.kafka.topics.rawMailMessageTopic=local_next_raw_mail
next.kafka.topics.templateMailMessageTopic=local_next_template_mail
next.kafka.topics.channelMessageTopic=uat_airbnb_message_topic
next.kafka.topics.messageSendTopic=local_next_send_message

# sla configs
sla.service.id.list = mongo
sla.service.mongo.type = mongo
sla.service.mongo.service = pcmsc-MongoDB

mail.template.console.reservation.url=https://pc.derbysoft-test.com/widget/reservation/detail
mail.template.console.url=https://pc.derbysoft-test.com

# dtrace
next.dtrace.service-name = PC-MSC
next.dtrace.agent-host = uat-dtrace.derbysoft-test.com
next.dtrace.agent-port= 6831
next.dtrace.reporter-log-spans = false
next.dtrace.sampler-type = probabilistic
next.dtrace.sampler-param = 0.1
next.dtrace.reporter-max-queue-size = 100000
next.dtrace.reporter-max-packet-size = 8960
next.dtrace.reporter-flush-interval = 1000

# mail group to+cc should be less than 50
mail.max.count=40