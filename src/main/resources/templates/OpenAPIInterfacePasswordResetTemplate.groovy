package templates

/**
 * @Created by <PERSON><PERSON><PERSON><PERSON> on 11/6/2024
 */

String fillingTemplate_EN(Map<String, Object> variables) {
    def template =  """<!DOCTYPE html><html lang="en"><head><meta charSet="UTF-8"/><link href="https://fonts.cdnfonts.com/css/roboto" rel="stylesheet"/><link href="https://fonts.cdnfonts.com/css/open-sans" rel="stylesheet"/><title></title></head><body style="margin:0;font-family:Inter, &quot;Noto Sans SC&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif"><table style="width:100%;padding:16px 24px;background:#F6F7F7;border:none;border-spacing:0;font-family:Inter, &quot;Noto Sans SC&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif"><tbody><tr><td align="center" style="padding:0"><table style="width:100%;height:100%;border:none;border-spacing:0;color:inherit;min-width:320px;max-width:600px;font-family:Inter, &quot;Noto Sans SC&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif"><tbody><tr><td style="padding:0"><table style="width:100%;height:100%;border:none;border-spacing:0;color:inherit;padding:40px;background:#FFFFFF;border-radius:24px;overflow:hidden;font-family:Inter, &quot;Noto Sans SC&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif"><tbody><tr><td style="padding:0"><img style="display:block;height:32px" src="https://neat-design-static.derbysoftsec.com/product-pc-by-derbysoft-logo.png"/></td></tr><tr><td style="height:48px;padding:0"></td></tr><tr><td style="padding:0"><table style="width:100%;height:100%;border:none;border-spacing:0;color:inherit;font-family:Inter, &quot;Noto Sans SC&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif"><tbody><tr><td style="padding:0"><h1 style="margin:0;font-weight:700;font-size:36px;line-height:44px;color:#00131C;font-family:Inter, &quot;Noto Sans SC&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif">Interface Password Reset</h1></td></tr><tr><td style="height:24px;padding:0"></td></tr><tr><td style="padding:0"><p style="margin:0;font-weight:400;font-size:16px;line-height:24px;color:black;white-space:pre-wrap;font-family:Inter, Segoe UI">Dear Partner,<br/><br/>We received a request to reset the password for your interface account and associated with this e-mail address.<br/><br/>Your interface user name is: <b>##pms_id##</b>.<br/>You may reset the password via <a href="##url##" style="color:#007bff;text-decoration:none">##url##</a> or the button below.</p></td></tr><tr><td style="height:24px;padding:0"></td></tr><tr><td style="padding:0"><a href="##url##" style="display:inline-block;text-decoration:none;height:40px;padding:0 16px;background:#00131C;border:none;border-radius:6px;vertical-align:middle;font-weight:500;font-size:16px;line-height:24px;color:#FFFFFF;cursor:pointer;font-family:Inter, &quot;Noto Sans SC&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif"><table style="width:100%;height:100%;border:none;border-spacing:0;color:inherit;font-family:Inter, &quot;Noto Sans SC&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif"><tbody><tr><td style="padding:0;vertical-align:middle">Reset</td></tr></tbody></table></a></td></tr><tr><td style="height:24px;padding:0"></td></tr><tr><td style="padding:0"><table style="width:100%;border:0px solid #F3F8FD;background-color:#F3F8FD;padding:15px;border-radius:6px;font-family:Arial, sans-serif;color:#333"><tbody><tr><td style="font-weight:bold;color:#007bff;width:20px;vertical-align:top;padding-top:5px"><img style="display:block;height:1rem" src="https://neat-design-static.derbysoftsec.com/info-circle-filled-link.png"/></td><td style="padding-left:10px"><p style="margin:0;font-weight:400;font-size:14px;line-height:24px;color:black;font-family:Inter, Noto Sans SC, PingFang SC, Microsoft YaHei, Arial, sans-serif">The link expires within ##expire_hours## hours! Please do not reply to this automatically generated email. This address is not monitored. For help please contact <a href="mailto:<EMAIL>" style="color:#007bff;text-decoration:none"><EMAIL></a></p></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table></td></tr><tr><td style="height:24px;padding:0"></td></tr><tr><td style="padding:0"><table style="width:100%;padding:40px 32px;border:none;border-spacing:0;font-family:Inter, &quot;Noto Sans SC&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif"><tbody><tr><td align="center" style="padding:0;line-height:0"><a href="https://derbysoft.com" target="_blank" rel="noreferrer" style="text-decoration:none;color:unset;display:inline-block"><img style="display:block;height:24px" src="https://neat-design-static.derbysoftsec.com/email-derbysoft-logo.png"/></a></td></tr><tr><td align="center" style="padding:0"><p style="margin:12px 0;line-height:20px;font-size:12px;color:#929A9E">DALLAS<!-- --> | <!-- -->SHANGHAI<!-- --> | <!-- -->TOKYO<!-- --> | <!-- -->BARCELONA</p></td></tr><tr><td align="center" style="padding:0;line-height:0"><a href="https://www.facebook.com/derbysoft" target="_blank" rel="noreferrer" style="text-decoration:none;color:unset;display:inline-block;margin-left:0"><img style="display:block;height:24px" src="https://neat-design-static.derbysoftsec.com/email-facebook-logo.png"/></a><a href="https://www.linkedin.com/company/derbysoft" target="_blank" rel="noreferrer" style="text-decoration:none;color:unset;display:inline-block;margin-left:24px"><img style="display:block;height:24px" src="https://neat-design-static.derbysoftsec.com/email-linkedin-logo.png"/></a><a href="https://twitter.com/derbysoft" target="_blank" rel="noreferrer" style="text-decoration:none;color:unset;display:inline-block;margin-left:24px"><img style="display:block;height:24px" src="https://neat-design-static.derbysoftsec.com/email-x-logo.png"/></a></td></tr><tr><td align="center" style="padding:0"><p style="margin:0 auto;line-height:18px;font-size:11px;color:#929A9E;margin-top:20px">Dallas Headquarters:<br/>14800 Landmark Blvd, Suite 640, Dallas TX 75254</p></td></tr><tr><td align="center" style="padding:0"><p style="margin:0 auto;line-height:18px;font-size:11px;color:#929A9E;margin-top:2px">© 2002 - 2024 DerbySoft Inc. All rights reserved.</p></td></tr><tr><td align="center" style="padding:0"><a href="https://derbysoft.com/privacy-policy/" target="_blank" rel="noreferrer" style="display:inline-block;text-decoration:none;height:32px;padding:0 16px;background:transparent;border:none;border-radius:6px;font-size:12px;font-weight:500;line-height:16px;color:#0D7EE0;cursor:pointer;margin:2px auto;font-family:Inter, &quot;Noto Sans SC&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif"><table style="width:100%;height:100%;border:none;border-spacing:0;color:inherit;font-family:Inter, &quot;Noto Sans SC&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif"><tbody><tr><td style="padding:0;vertical-align:middle">Privacy Policy</td></tr></tbody></table></a></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table></body></html>"""
    template = template.replaceAll(/##(.+?)##/) {
        variables[it[1]]
    }

    return template
}