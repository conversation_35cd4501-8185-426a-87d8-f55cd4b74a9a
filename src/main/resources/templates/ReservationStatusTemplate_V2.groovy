import com.google.common.base.Joiner
import org.apache.commons.lang3.StringUtils
import com.derbysoft.next.sample.util.DailyRateUtil
import java.text.DecimalFormat

String fillingTemplate_EN(Map<String, Object> variables) {
    def errorModule="""
        <tr>
            <td style="text-align: center;">
                <table role="presentation" cellpadding="0" cellspacing="0"
                       style="background:#fff;font-size:0px;width:800px;display: inline-table;padding-bottom: 8px"
                       border="0">
                    <tr>
                        <td style="text-align: center; padding-bottom: 16px;">
                            <table role="presentation" cellpadding="0" cellspacing="0"
                                   style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                   border="0">
                                <tr>
                                    <td><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">Error</p></td>
                                </tr>
                                <tr>
                                    <td style="width: 100%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Error Code</p></td>
                                </tr>
                                <tr>
                                    <td style="width: 100%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        ${variables.errorCode ?: ''}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 100%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Error Message</p></td>
                                </tr>
                                <tr>
                                    <td style="width: 100%;vertical-align: text-top;">
                                        <p style="font-size: 14px;margin:0;word-break: break-all;">
                                        ${variables.errorMessage ?: ''}</p>
                                    </td>
                                    
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    """
    // 20230117-add by ld (**********) start
    def suggestionModule="""
        <tr>
            <td style="text-align: center;">
                <table role="presentation" cellpadding="0" cellspacing="0"
                       style="background:#fff;font-size:0px;width:800px;display: inline-table;padding-bottom: 8px"
                       border="0">
                    <tr>
                        <td style="text-align: center; padding-bottom: 16px;">
                            <table role="presentation" cellpadding="0" cellspacing="0"
                                   style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                   border="0">
                                <tr>
                                    <td><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">SUGGESTION</p></td>
                                </tr>
                                <tr>
                                    <td style="width: 100%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Suggestion</p></td>
                                </tr>
                                <tr>
                                    <td style="width: 100%;vertical-align: text-top;">
                                        <p style="font-size: 14px;margin:0;word-break: break-all;">
                                        ${Optional.ofNullable(variables?.suggestion?.message as Map).map(e -> e.get('en-US')).orElse('')}</p>
                                    </td>
                                    
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    """
    // 20230117-add by ld (**********) end
    def paymentMethod = """
            <tr>
                <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Payment Method</p></td>
            </tr>
            <tr>
                <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">${variables.transactionInfo?.hotelSystemTxn?.txnProvider ?: '-'}</p></td>
            </tr>
    """


    // 2024/4/16-modify by liucl (**********) start
    def resReplaceFlg = false;
    if (variables.roomRates != null && variables.roomRates.size() > 0 ) {
        resReplaceFlg = StringUtils.isNotBlank(variables.roomRates[0].replaceType)
    }
    def zeroWay = Objects.equals("ZeroWay",variables.reservationType)

    def warningModule = """
        <tr>
            <td style="text-align: center;">
                <table role="presentation" cellpadding="0" cellspacing="0"
                       style="background:#fff;font-size:0px;width:800px;display: inline-table;padding-bottom: 8px"
                       border="0">
                    <tr>
                        <td style="text-align: center; padding-bottom: 16px;">
                            <table role="presentation" cellpadding="0" cellspacing="0"
                                   style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                   border="0">
                                <tr>
                                    <td><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">WARNING</p></td>
                                </tr>
                                <tr>
                                    <td style="width: 100%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Warning</p></td>
                                </tr>
                                <tr>
                                    <td style="width: 100%;vertical-align: text-top;">
                                        <p style="font-size: 14px;margin:0;word-break: break-all;">
                                        The room rate are replaced with the default settings, please verify in your PMS if needed.</p>
                                    </td>
                                    
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    """
    // 2024/4/16-modify by liucl (**********) end
    def  decimalFormat = new DecimalFormat("###################.###########");

    def taxBoth = """
         ${variables.currency ?: '-'}
         <br>BeforeTax: ${variables.hotelSystemAmountBeforeTax ?: '-'}
         <br>AfterTax: ${variables.hotelSystemAmountAfterTax ?: '-'}
    """
    def taxAnyone="""
        ${variables.hotelSystemAmountBeforeTax ?: ''}
        ${variables.hotelSystemAmountAfterTax ?: ''}
        ${variables.currency ?: ''}
    """
    def dailyRates=DailyRateUtil.dailyRates(variables.checkin,variables.checkout,variables.roomRates[0].hotelSystemAmountBeforeTax,variables.roomRates[0].hotelSystemAmountAfterTax,variables.currency)
    def dailyRatePrettyStr=''
    dailyRates.each {
        dailyRatePrettyStr = dailyRatePrettyStr + it
        dailyRatePrettyStr = dailyRatePrettyStr + '<br>'
    }


    def mixResultSuccess="""
        <span style="background: #4CAF50;color: #ffffff; padding: 3px 8px; border-radius: 4px; font-weight: normal;">${variables.mixDisplayResult ?: '-'}</span>  
    """
    def pmsResultSuccess="""
        <span style="background: #EDF7EE;color: #1E860D; padding: 3px 8px; border-radius: 4px; font-weight: normal;">${variables.hotelSystemDisplayResult ?: '-'}</span>  
    """

    def mixResultProcessing="""
        <span style="background: #2DB7F5;color: #ffffff; padding: 3px 8px; border-radius: 4px; font-weight: normal;">${variables.mixDisplayResult ?: '-'}</span>  
    """
    def pmsResultProcessing="""
        <span style="background: #F3F8FB;color: #0D5A86; padding: 3px 8px; border-radius: 4px; font-weight: normal;">${variables.hotelSystemDisplayResult ?: '-'}</span>  
    """

    def mixResultFailed="""
        <span style="background: #FF5959;color: #ffffff; padding: 3px 8px; border-radius: 4px; font-weight: normal;">${variables.mixDisplayResult ?: '-'}</span>  
    """
    def pmsResultFailed="""
        <span style="background: #FEF4F4;color: #A71717; padding: 3px 8px; border-radius: 4px; font-weight: normal;">${variables.hotelSystemDisplayResult ?: '-'}</span>  
    """

    def template = """
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
    <head>
        <meta charset="UTF-8">
        <title>${variables.channelName ?: '-'} Reservation</title>
    </head>
    <body style="margin: 0;padding: 0">
        <table role="presentation" cellpadding="0" cellspacing="0" style="width:100%;background-color: #f8f9ff;" border="0">
            <!--        header            -->
            <tr>
              <td style="text-align: center;">
                <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="font-size:0px;width:800px;background-color: #fff;border-top-left-radius: 4px;border-top-right-radius: 4px;margin-top: 16px;" border="0">
                  <tbody>
                    <tr>
                      <td style="padding: 30px 48px;text-align: left; width:460px;">
                        <img style="border: none;border-radius: 0px;outline: none;text-decoration: none;width: 256px;height: auto;display: inline-block;vertical-align: middle;" src="https://pc.derbysoftsec.com/public/images/logoColor.png" alt="">
                      </td>
                      <td>
                        <a href="${variables.consoleUrl}/widget/reservation/detail?derbyResIdNo=${variables.derbyResId}&propertyId=${variables.hotelId}" style="display: block;width: 210px;height: 40px;border-radius: 4px;background: #FFFFFF; font-size: 14px;
                          border: 1px solid #E1E1E1; color: #004771 !important;line-height: 40px;text-align: center;font-weight: 600;text-decoration: none;">
                          View Reservation
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </td>
            </tr>
            <tr>
              <td style="text-align: center;">
                <table role="presentation" cellpadding="0" cellspacing="0" style="width:800px; height:80px; display: inline-table;">
                  <tbody>
                    <tr>
                      <td style="text-align: center;width:800px; height:80px; background-color: #004771;">
                        <p style="color: #fff;font-size:24px; height:30px; line-height: 30px;font-weight: bold;text-align: center;">
                          ${variables.channelName ?: '-'} Reservation
                        </p>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </td>
            </tr>

            <!--       content            -->
            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:800px;display: inline-table;padding-bottom: 8px"
                           border="0">

                        <tr>
                            <td style="text-align: center; padding-bottom: 16px;">
                                <table role="presentation" cellpadding="0" cellspacing="0"
                                       style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                       border="0">
                                    <tr>
                                        <td colspan="2"><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">OVERVIEW   
                                            ${'Failed' == variables.finalResult ?mixResultFailed:''}
                                            ${'Successful' == variables.finalResult ?mixResultSuccess:''}
                                            ${'Processing' == variables.finalResult ?mixResultProcessing:''}
                                            ${variables.hotelSystemDisplayResult? ('Failed' == variables.hotelSystemResult ?pmsResultFailed:'') :'' }
                                            ${variables.hotelSystemDisplayResult? ('Successful' == variables.hotelSystemResult ?pmsResultSuccess:'') :'' }
                                            ${variables.hotelSystemDisplayResult? ('Processing' == variables.hotelSystemResult ?pmsResultProcessing:'') :'' }                                            
                                            ${variables.timeoutFlg || variables.warningFlg || resReplaceFlg ?
                                            '<span style="background: #FEF4F4;padding: 3px 8px; border-radius: 4px;color: #A71717; font-weight: normal;">Warning</span>' : ''}
                                            </p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Name</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Channel</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            <p style="font-size: 14px;margin: 0; font-weight: bold;">
                ${variables.guests != null && variables.guests.size() > 0 ? Joiner.on(" ").join(variables.guests[0].firstName, variables.guests[0].lastName) ?: '-' : '-'}
                                            </p>
                                        </td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        ${variables.channelName ?: '-'}</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Arrival</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Property</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            ${variables.checkin ?: '-'}</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            [${variables.hotelId}] ${variables.hotelName ?: '-'}</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Departure</p></td>
                                    </tr>
                                    <tr>
                                        <td colspan="2"><p style="font-size: 14px;margin:0; ">${variables.checkout ?: '-'} </p></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:800px;display: inline-table;padding-bottom: 8px"
                           border="0">

                        <tr>
                            <td style="text-align: center; padding-bottom: 16px;">
                                <table role="presentation" cellpadding="0" cellspacing="0"
                                       style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                       border="0">
                                    <tr>
                                        <td colspan="2"><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">DETAILS</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">PMS RES ID</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Channel RES ID</p></td>
                                    </tr>
                                     <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            ${variables.hotelSystemResId ?: '-'}</p>
                                        </td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            ${variables.channelResId ?: '-'}</p></td>
                                    </tr>
                                    
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">PMS Cancel RES ID</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Derby RES ID</p></td>
                                    </tr>
                                     <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            ${variables.hotelSystemCancellationId ?: '-'}</p>
                                        </td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            ${variables.derbyResId ?: '-'}</p></td>
                                    </tr>

                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Rate Type</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Booking Time</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            ${variables.hotelSystemRateType  ?: '-'}</p>
                                        </td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            ${variables.createDate ?: '-'}</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Amount Before Tax</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Last Update Time</p></td>
                                    </tr>
                                    <tr>
                                         <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            ${variables.hotelSystemAmountBeforeTax != null? (variables.hotelSystemAmountBeforeTax +" "+ variables.currency) :'-'}</p>
                                         </td> 
                                         <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            ${variables.lastModifyDate ?: '-'}</p>
                                         </td>
                                    </tr>
                                    
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Amount After Tax</p></td>
                                    </tr>
                                    <tr>
                                         <td colspan="2"><p style="font-size: 14px;margin:0;">
                                            ${variables.hotelSystemAmountAfterTax != null? (variables.hotelSystemAmountAfterTax +" "+ variables.currency) :'-'}</p>
                                         </td> 
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Daily Rates</p></td>
                                    </tr>
                                    <tr>
                                        <td colspan="2"><p style="font-size: 14px;margin:0;">
                                        ${dailyRatePrettyStr}
                                        </p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Comments</p></td>
                                    </tr>
                                    <tr>
                                        <td colspan="2"><p style="font-size: 14px;margin:0;">
                                        ${variables.comments?.findAll { StringUtils.isNotBlank(it) }?.join(',') ?: '-'}</p></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            ${variables.transactionAmounts != null ? fillFinancialDetails(variables.currency, variables.transactionAmounts) : ''}
            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:800px;display: inline-table;padding-bottom: 8px"
                           border="0">
                        <tr>
                            <td style="text-align: center; padding-bottom: 16px;">
                                <table role="presentation" cellpadding="0" cellspacing="0"
                                       style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                       border="0">
                                    <tr>
                                        <td colspan="2"><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">ROOM RATE</p></td>
                                    </tr>
                                    
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Rate Plan</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Channel Rate Plan</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        [${variables.roomRates != null && variables.roomRates.size() > 0 ? variables.roomRates[0].rateId ?: '-' : '-'}] ${variables.roomRates != null && variables.roomRates.size() > 0 ? variables.roomRates[0].rateName ?: '-' : '-'}</p>
                                        </td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        [${variables.roomRates != null && variables.roomRates.size() > 0 ? variables.roomRates[0].channelRateId ?: '-' : '-'}] ${variables.roomRates != null && variables.roomRates.size() > 0 ? variables.roomRates[0].rateName ?: '-' : '-'}</p>
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Room Type</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Channel Room Type</p></td>
                                    </tr>
                                     <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        [${variables.roomRates != null && variables.roomRates.size() > 0 ? variables.roomRates[0].roomId ?: '-' : '-'}] ${variables.roomRates != null && variables.roomRates.size() > 0 ? variables.roomRates[0].roomName ?: '-' : '-'}</p>
                                        </td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        [${variables.roomRates != null && variables.roomRates.size() > 0 ? variables.roomRates[0].channelRoomId ?: '-' : '-'}] ${variables.roomRates != null && variables.roomRates.size() > 0 ? variables.roomRates[0].roomName ?: '-' : '-'}</p>
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Room Count</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Guest Count</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        ${variables.roomCount ? decimalFormat.format(variables.roomCount): '-'}</p>
                                        </td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        Adults: ${variables.adultCount ?decimalFormat.format(variables.adultCount): '-'}<br/>
                                        Childs: ${variables.childCount ?decimalFormat.format(variables.childCount): '-'}</p></td>
                                    </tr>
                                 
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Meal Plan</p></td>
                                    </tr>
                                    <tr>
                                        <td colspan="2"><p style="font-size: 14px;margin:0; ">${variables.mealPlanDescription ?: '-'} </p></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <!--package -->
            ${variables.packageInfos != null && variables.packageInfos.size() >0 ? fillPackageInfo(zeroWay,variables.packageInfos) : ''}

            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:800px;display: inline-table;padding-bottom: 8px"
                           border="0">

                        <tr>
                            <td style="text-align: center; padding-bottom: 16px;">
                                <table role="presentation" cellpadding="0" cellspacing="0"
                                       style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                       border="0">
                                    <tr>
                                        <td colspan="3"><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">PAYMENT INFO</p></td>
                                    </tr>
                                      <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Payment Type</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Guarantee Type</p></td>
                                        </tr>
                                        <tr>
                                            <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                             ${variables.paymentType ?: '-'}</p>
                                            </td>
                                            <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                             ${variables.guaranteeType ?: '-'}</p>
                                            </td>
                                        </tr>
                                        ${variables.transactionInfo?.channelTxn?.txnProvider ? paymentMethod: ''}
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:800px;display: inline-table;padding-bottom: 8px"
                           border="0">

                        <tr>
                            <td style="text-align: center; padding-bottom: 16px;">
                                <table role="presentation" cellpadding="0" cellspacing="0"
                                       style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                       border="0">
                                    <tr>
                                        <td colspan="3"><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">CONTACT PERSON</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 30%;"><p style="font-size: 14px;margin:14px 0 4px; font-weight: bold;">
                                            ${variables.contactPerson ? Joiner.on(" ").join(variables.contactPerson.firstName, variables.contactPerson.lastName) ?: '-' : '-'}
                                            </p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:14px 0 4px;">
                                            ${variables.contactPerson?.email ?: '-'}</p></td>
                                        <td style="width: 20%;"><p style="font-size: 14px;margin:14px 0 4px;">
                                            ${variables.contactPerson?.phone ?: '-'}</p></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            
            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:800px;display: inline-table;padding-bottom: 8px"
                           border="0">

                        <tr>
                            <td style="text-align: center; padding-bottom: 16px;">
                                <table role="presentation" cellpadding="0" cellspacing="0"
                                       style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                       border="0">
                                    <tr>
                                        <td colspan="3"><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">GUEST</p></td>
                                    </tr>
                                    ${variables.guests?.withIndex()?.collect { guest, index ->
                                    """
                                    <tr>
                                        <td style="width: 30%;"><p style="font-size: 14px;margin:14px 0 4px; font-weight: bold;">
                                            ${Joiner.on(" ").join(guest?.firstName, guest?.lastName) ?: '-'}</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:14px 0 4px;">
                                            ${guest?.email ?: '-'}</p></td>
                                        <td style="width: 20%;"><p style="font-size: 14px;margin:14px 0 4px;">
                                            ${guest?.phone ?: '-'}</p></td>
                                    </tr>
                                        """
                                    }?.join('') ?: ''
                                }
                                </table>
                            </td>
                        </tr>
                        
                    </table>
                </td>
            </tr>
            
            ${'Failed' == variables.finalResult ? errorModule:''}
            ${'Failed' == variables.finalResult || variables.timeoutFlg ? suggestionModule:''}
            ${ resReplaceFlg ? warningModule:''}

            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:800px;display: inline-table;padding-bottom: 8px"
                           border="0">
                        <tr align="center" style="width: 800px;">
                          <td style="text-decoration: none;padding: 36px 0;font-size: 14px; background-color: #FFF; margin:0 auto;width: 800px;">
                            <a href="${variables.consoleUrl}/widget/reservation/detail?derbyResIdNo=${variables.derbyResId}&propertyId=${variables.hotelId}" style="display: block;width: 210px;height: 40px;border-radius: 4px;background: #004771;color: white !important;line-height: 40px;text-align: center;font-weight: 600;text-decoration: none;">
                              View Reservation
                            </a>
                          </td>
                        </tr>            
                    </table>
                </td>
            </tr>
            
            <!-- Footer start-->
            <tr>
              <td style="text-align: center;padding: 0">
                <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="background:#fff;font-size:0px;width:800px;height:260px;display: inline-table; border-top: 1px solid #EEEFF0;border-bottom-left-radius: 4px;border-bottom-right-radius: 4px;margin-bottom:16px;" border="0">
                  <tbody>
                    <tr>
                      <td style="text-align: center;padding: 0">
                        <a style="width: 110px;margin:32px auto 0;display: inline-block;" href="https://derbysoft.com">
                          <img src="https://pc.derbysoftsec.com/public/images/icon-derbysoft.png" style="border: none;border-radius: 0px;display: block;outline: none;text-decoration: none;width: 100%;height: auto;" alt="DerbySoft">
                        </a>
                      </td>
                    </tr>
                    <tr>
                      <td style="padding: 0">
                        <p style="width:430px;font-size: 12px;margin: 0 auto 18px;text-align: center;color: #999">
                            DALLAS | SHANGHAI | BEIJING | TOKYO | NANJING | BARCELONA
                        </p>
                        <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="width:800px;height:32px;display: inline-table;margin-bottom: 18px" border="0">
                          <tbody style="text-align: center">
                            <tr> 
                              <td>
                                <a style="width: 32px;display: inline-block;margin-right: 24px" href="https://www.facebook.com/derbysoft" target="_blank">
                                  <img src="https://pc.derbysoftsec.com/public/images/icon-facebook.png" style="border: none;border-radius: 0px;display: block;outline: none;text-decoration: none;width: 100%;height: auto;">
                                </a>
                                <a style="width: 32px;display: inline-block;margin-right: 24px" href="https://www.linkedin.com/company/derbysoft/" target="_blank">
                                  <img src="https://pc.derbysoftsec.com/public/images/icon-linkedin.png" style="border: none;border-radius: 0px;display: block;outline: none;text-decoration: none;width: 100%;height: auto;">
                                </a>
                                <a style="width: 32px;display: inline-block;" href="https://twitter.com/derbysoft" target="_blank">
                                  <img src="https://pc.derbysoftsec.com/public/images/icon-twitter.png" style="border: none;border-radius: 0px;display: block;outline: none;text-decoration: none;width: 100%;height: auto;">
                                </a>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                        <p style="width:430px;font-size: 12px;margin: 0 auto;text-align: center;color: #999">
                            Dallas Headquarters:
                        </p>
                        <p style="width:430px;font-size: 12px;margin: 0 auto 8px;text-align: center;color: #999">
                            14800 Landmark Blvd, Suite 640, Dallas TX 75254
                        </p>
                        <!-- <p style="width:430px;font-size: 12px;margin: 0 auto;text-align: center;color: #999">
                            Copyright © 2018 DerbySoft Ltd. All rights reserved. | <a href="https://www.derbysoft.com/privacy-policy" style="color: #004771; text-decoration: none;">Privacy Policy</a>
                        </p> -->
                      </td>
                    </tr>
                  </tbody>
                </table>
              </td>
            </tr>
            <!-- Footer end-->

        </table>

    </body>
</html>
"""
    return template
}

String fillPackageInfo(boolean zeroWay,List packageInfos) {
    if (Objects.nonNull(packageInfos) && packageInfos.size() > 99) {
        packageInfos = packageInfos.subList(0,99)
    }
    return """<tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:800px;display: inline-table;padding-bottom: 8px"
                           border="0">
                        <tr>
                            <td style="text-align: center; padding-bottom: 16px;">
                                <table role="presentation" cellpadding="0" cellspacing="0"
                                       style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                       border="0">
                                    <tr>
                                        <td colspan="2"><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">PACKAGE</p></td>
                                    </tr>
                                    ${packageInfos?.withIndex()?.collect {packageInfo,index ->
                                        """<tr>
                                                <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Package</p></td>
                                                <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Channel Package Code</p></td>
                                            </tr>
                                            <tr>
                                                <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                                ${zeroWay? (packageInfo.packageInfo?.pkgName ?:'-'):('[' + (packageInfo.packageInfo?.pkgId ?:'-') + '] ' + (packageInfo.packageInfo?.pkgName ?:'-'))}</p>
                                                </td>
                                                <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                                ${packageInfo.pkgId ?:'-'}</p>
                                                </td>
                                            </tr>
        
                                            <tr>
                                                <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Total Price</p></td>
                                                <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Daily Price</p></td>
                                            </tr>
                                            <tr>
                                                <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                                ${((packageInfo.amountTotalBeforeTax?:(packageInfo.amountTotalAfterTax?:'-')) + " " + packageInfo.currency)?:'-'}</p>
                                                </td>
                                                <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                               ${packageInfo.dailyPrice?.withIndex()?.collect { daily,i ->
                                                    daily.stayDate + ": " + (daily.amountBeforeTax ?: (daily.amountAfterTax ?: '-')) + " " + packageInfo.currency
                                                }?.join(" <br />") ?: ''}
                                                </p></td>
                                            </tr>"""
                                    }?.join('')?:''}
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>"""
}

String fillFinancialDetails(String currency, def transactionAmounts) {
    return """
            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:800px;display: inline-table;padding-bottom: 8px"
                           border="0">
                        <tr>
                            <td style="text-align: center; padding-bottom: 16px;">
                                <table role="presentation" cellpadding="0" cellspacing="0"
                                       style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                       border="0">
                                    <tr>
                                        <td colspan="2"><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">FINANCIAL DETAILS</p></td>
                                    </tr>
                                    
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Amount Receivable</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Paid Amount</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        ${(transactionAmounts.currency?:(currency?:'-')) + " " + (transactionAmounts.amountReceivable?:'-')}</p>
                                        </td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        ${(transactionAmounts.currency?:(currency?:'-')) + " " + (transactionAmounts.amountPaid?:'-')}</p>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>"""
}