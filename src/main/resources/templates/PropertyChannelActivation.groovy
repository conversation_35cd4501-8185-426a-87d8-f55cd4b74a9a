package templates

String fillingTemplate_EN(Map<String, Object> variables) {
    def template = """
<!DOCTYPE html>
<html>
<head>
  <title>Notice of Property Channel Connection Activation</title>
  <meta />
  <meta name="viewport" content="width=device-width" />
  <meta />
</head>
<body style="width: 100% !important;height: 100% !important;margin: 0;padding: 0;color: #666;border-collapse: collapse !important;">
  <table align="center" style="background: #f8f9ff; width: 100%; margin: 0 auto">
    <tr style="background: #f8f9ff">
      <td style="padding: 0">
        <table align="center" width="800px" style="border-radius: 4px; margin: 0 auto" border="0" cellpadding="0" cellspacing="0" width="100%">
          <!-- Header start -->
          <tr>
            <td style="text-align: center">
              <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="font-size: 0px; width: 800px; background-color: #fff; border-top-left-radius: 4px; border-top-right-radius: 4px; margin-top: 16px" border="0">
                <tbody>
                  <tr>
                    <td style="padding: 30px 48px; text-align: left">
                      <img style="border: none; border-radius: 0px; outline: none; text-decoration: none; width: 256px; height: auto; display: inline-block; vertical-align: middle;" src="https://pc.derbysoftsec.com/public/images/logoColor.png" alt="" />
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>

          <tr>
            <td style="text-align: center">
              <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="width: 800px; height: 160px; display: inline-table" border="0">
                <tbody>
                  <tr>
                    <td style="text-align: center; width: 800px; height: 160px; background-color: #004771">
                      <p style="color: #fff; font-size: 24px; height: 30px; line-height: 30px; font-weight: bold; text-align: center; margin: 0; margin-top: 24px">
                        Notice of Property Channel Connection Activation
                      </p>
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <!-- Header end -->

          <!-- content start -->
          <tr>
            <td>
              <table align="center" style="background: #fff; font-size: 0px; width: 800px" border="0">
                <tr>
                  <td>
                    <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="background: #fff; font-size: 0px; width: 100%; display: inline-block; padding: 24px 48px 64px" border="0">
                      <tr>
                        <td>
                          <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" align="center" style="table-layout: fixed; border: 1px solid #eeeeee">
                            <tr style="border-bottom: 1px solid #eeeeee">
                              <td style="text-align: left; font-size: 14px; background-color: #f4f4f4; padding: 0 8px; border-right: 1px solid #eeeeee; width: 126px">
                                Property
                              </td>
                              <td style="text-align: left; font-size: 14px; word-break: break-all; color: #666; padding: 16px 10px; border-right: 1px solid #eeeeee">
                                <span>[##propertyId##]##propertyName##</span>
                              </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #eeeeee">
                              <td style="text-align: left; font-size: 14px; background-color: #f4f4f4; padding: 0 8px; border-right: 1px solid #eeeeee; width: 126px">
                                Channel
                              </td>
                              <td style="text-align: left; font-size: 14px; word-break: break-all; color: #666; padding: 16px 10px; border-right: 1px solid #eeeeee">
                                <span>##channelName##</span>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                      <tr>
                        <td style="text-decoration: none; padding-top: 42px; font-size: 14px">
                          <a href="##consoleUrl##/widget/administration/propertyGroupList" style="display: block; width: 306px; height: 40px; border-radius: 4px; background: #004771; color: white !important; line-height: 40px; text-align: center; font-weight: 600; text-decoration: none; margin: 0 auto">
                            Open Property Connector Administration
                          </a>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <!-- content end -->

          <!-- Footer start-->
          <tr>
            <td style="text-align: center; padding: 0">
              <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="background: #fff; font-size: 0px; width: 800px; height: 260px; display: inline-table; border-top: 1px solid #eeeff0; border-bottom-left-radius: 4px; border-bottom-right-radius: 4px; margin-bottom: 16px" border="0">
                <tbody>
                  <tr>
                    <td style="text-align: center; padding: 0">
                      <a style="width: 110px; margin: 32px auto 0; display: inline-block; color: #666 !important; text-decoration: none !important;" href="https://derbysoft.com">
                        <img src="https://pc.derbysoftsec.com/public/images/icon-derbysoft.png" style="border: none; border-radius: 0px; display: block; outline: none; text-decoration: none; width: 100%; height: auto" alt="DerbySoft" />
                      </a>
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 0">
                      <p style="width: 430px; font-size: 12px; margin: 0 auto 18px; text-align: center; color: #999; line-height: 24px">
                        DALLAS | SHANGHAI | TOKYO | BARCELONA
                      </p>
                      <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="width: 800px; height: 32px; display: inline-table; margin-bottom: 18px" border="0">
                        <tbody style="text-align: center">
                          <tr>
                            <td>
                              <a style="width: 32px; display: inline-block; margin-right: 24px; color: #666 !important; text-decoration: none !important;" href="https://www.facebook.com/derbysoft" target="_blank">
                                <img src="https://pc.derbysoftsec.com/public/images/icon-facebook.png" style="border: none; border-radius: 0px; display: block; outline: none; text-decoration: none; width: 100%; height: auto" />
                              </a>
                              <a style="width: 32px; display: inline-block; margin-right: 24px; color: #666 !important; text-decoration: none !important;" href="https://www.linkedin.com/company/derbysoft/" target="_blank">
                                <img src="https://pc.derbysoftsec.com/public/images/icon-linkedin.png" style="border: none; border-radius: 0px; display: block; outline: none; text-decoration: none; width: 100%; height: auto" />
                              </a>
                              <a style="width: 32px; display: inline-block; color: #666 !important; text-decoration: none !important;" href="https://twitter.com/derbysoft" target="_blank">
                                <img src="https://pc.derbysoftsec.com/public/images/icon-twitter.png" style="border: none; border-radius: 0px; display: block; outline: none; text-decoration: none; width: 100%; height: auto" />
                              </a>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <p style="width: 430px; font-size: 12px; margin: 0 auto; text-align: center; color: #999; line-height: 24px">
                        Dallas Headquarters:
                      </p>
                      <p style="width: 430px; font-size: 12px; margin: 0 auto 8px; text-align: center; color: #999; line-height: 24px">
                        14800 Landmark Blvd, Suite 640, Dallas TX 75254
                      </p>
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <!-- Footer end-->
        </table>
      </td>
    </tr>
  </table>
</body>
</html>
"""
    template = template.replaceAll(/##(.+?)##/) {
        variables[it[1]]
    }

    return template
}