import com.google.common.base.Joiner
import org.apache.commons.lang3.StringUtils
import com.derbysoft.next.sample.util.DailyRateUtil

String fillingTemplate_EN(Map<String, Object> variables) {
    def mappingError = """
            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:680px;display: inline-table;padding-bottom: 8px"
                           border="0">

                        <tr>
                            <td style="text-align: center; ">
                                <table role="presentation" cellpadding="0" cellspacing="0"
                                       style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                       border="0">
                                    <tr>
                                        <td colspan="2">
                                        <p style="font-size: 14px;margin: 0; padding: 16px 0 8px;font-weight: bold;">
                                        您好，<br/><br/>您的以下订单因产品映射未匹配而失败，请检查您的产品映射。<br/><br/>
                                           
                                        </p></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                
                </td>
            </tr>
"""
    // 2022/03/26-modify by zheng.yu (OPRC-2991) start
    def meituanError = """
            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:680px;display: inline-table;padding-bottom: 8px"
                           border="0">

                        <tr>
                            <td style="text-align: center; ">
                                <table role="presentation" cellpadding="0" cellspacing="0"
                                       style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                       border="0">
                                    <tr>
                                        <td colspan="2">
                                        <p style="font-size: 14px;margin: 0; padding: 16px 0 8px;font-weight: bold;">
                                        您好，<br/><br/>您的订单已下单失败，请检查您PMS设置，产品映射是否正确。修改后您可在Property connector订单页面重试此订单。<br/><br/>
                                           
                                        </p></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                
                </td>
            </tr>
"""
    // 2022/03/26-modify by zheng.yu (OPRC-2991) end

    def taxBoth = """
         ${variables.currency ?: '-'}
         <br>BeforeTax: ${variables.hotelSystemAmountBeforeTax ?: '-'}
         <br>AfterTax: ${variables.hotelSystemAmountAfterTax ?: '-'}
    """
    def taxAnyone="""
        ${variables.hotelSystemAmountBeforeTax ?: ''}
        ${variables.hotelSystemAmountAfterTax ?: ''}
        ${variables.currency ?: ''}
    """
    def dailyRates=DailyRateUtil.dailyRates(variables.checkin,variables.checkout,variables.roomRates[0].hotelSystemAmountBeforeTax,variables.roomRates[0].hotelSystemAmountAfterTax,variables.currency)
    def dailyRatePrettyStr=''
    dailyRates.each {
        dailyRatePrettyStr = dailyRatePrettyStr + it
        dailyRatePrettyStr = dailyRatePrettyStr + '<br>'
    }

    def pcResult="""
        <span style="background: #004771;color: #ffffff; padding: 0 8px; border-radius: 4px; font-weight: normal;">${variables.status ?: '-'}</span>  
        <span style="background: #06789c;padding: 0 8px; border-radius: 4px;color: #ffffff; font-weight: normal;">${variables.result ?: '-'}</span> 
    """
    def pmsResult="""
        <span style="background: #004771;color: #ffffff; padding: 0 8px; border-radius: 4px; font-weight: normal;">${variables.hotelSystemStatus ?: '-'}</span>  
        <span style="background: #06789c;padding: 0 8px; border-radius: 4px;color: #ffffff; font-weight: normal;">${variables.hotelSystemResult ?: '-'}</span> 
    """

    def template = """
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
    <head>
        <meta charset="UTF-8">
        <title>${variables.channelName ?: '-'} Reservation</title>
    </head>
    <body style="margin: 0;padding: 0">
        <table role="presentation" cellpadding="0" cellspacing="0" style="width:100%;background-color: #f8f9ff;"
               border="0">
            <!--        header            -->
            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="font-size:0px;width:680px;height:48px; display: inline-table;background-color: #fff"
                           border="0">
                        <tr>
                            <td style="padding: 16px;text-align: center">
                                <img style="border: none;border-radius: 0px;
                            outline: none;text-decoration: none;width: 155px;height: auto;display: inline-block;"
                                     src="https://info.derbysoft.com/rs/175-BXB-006/images/click-logo%402x.png"
                                     alt="">
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="width:680px; height:60px; display: inline-table;"
                           border="0">
                        <tr>
                            <td style="text-align: center;width:680px; height:60px; background-color: #004771;border-top: 1px solid #004771;border-bottom: 1px solid #004771;">
                                <p style="color: #fff;font-size:18px; height:30px; line-height: 30px;font-weight: bold;text-align: center;">
                                    ${variables.channelName ?: '-'} Reservation</p>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <!--       content            -->
            <!--2022/03/26-modify by zheng.yu (OPRC-2991) start-->
            ${'MEITUANDT' == variables.channelId ? (variables.errorCode != null && variables.errorCode != '' ? meituanError : '') : ('channelProductMappingNotFound' == variables.errorCode ? mappingError:'')}
            <!--2022/03/26-modify by zheng.yu (OPRC-2991) end-->
            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:680px;display: inline-table;padding-bottom: 8px"
                           border="0">

                        <tr>
                            <td style="text-align: center; padding-bottom: 16px;">
                                <table role="presentation" cellpadding="0" cellspacing="0"
                                       style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                       border="0">
                                    <tr>
                                        <td colspan="2"><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">OVERVIEW   
                                            ${variables.useHotelSystemResultFlg ? pmsResult:pcResult}
                                            ${variables.timeoutFlg?
                                            '<span style="background: #f44336;padding: 0 8px; border-radius: 4px;color: #ffffff; font-weight: normal;">Channel Timeout</span>' : ''}
                                            ${variables.warningFlg?
                                            '<span style="background: #f44336;padding: 0 8px; border-radius: 4px;color: #ffffff; font-weight: normal;">Warning</span>' : ''}        
                                        </p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;">
                                            <p style="font-size: 14px;margin:14px 0 4px; font-weight: bold;">
                ${variables.guests != null && variables.guests.size() > 0 ? Joiner.on(" ").join(variables.guests[0].firstName, variables.guests[0].lastName) ?: '-' : '-'}
                                            </p>
                                        </td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:14px 0 4px; ">
                                        Check-in: ${variables.checkin ?: '-'}</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:14px 0 4px; ">
                                        ${variables.channelName ?: '-'}</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:14px 0 4px; ">
                                        Check-out: ${variables.checkout ?: '-'}</p></td>
                                    </tr>
                                    <tr>
                                        <td colspan="2"><p style="font-size: 14px;margin:14px 0 4px; ">${variables.hotelId} ${variables.hotelName ?: '-'}</p></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:680px;display: inline-table;padding-bottom: 8px"
                           border="0">

                        <tr>
                            <td style="text-align: center; padding-bottom: 16px;">
                                <table role="presentation" cellpadding="0" cellspacing="0"
                                       style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                       border="0">
                                    <tr>
                                        <td colspan="2"><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">RESERVATION DETAILS</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Derby RES ID:</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Supply RES ID:</p></td>
                                    </tr>
                                     <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            ${variables.derbyResId ?: '-'}</p>
                                        </td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            ${variables.pcResId ?: '-'}</p></td>
                                    </tr>
                                    
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Distributor RES ID:</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Supply Cancel RES ID:</p></td>
                                    </tr>
                                     <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            ${variables.channelResId ?: '-'}</p>
                                        </td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            ${variables.pcCancellationId ?: '-'}</p></td>
                                    </tr>

                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Rate Type:</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Booking Time:</p></td>
                                    </tr>
                                    <tr>
                                       <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        ${variables.hotelSystemRateType ?: '-'}</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                            ${variables.createDate ?: '-'}</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Total Rates:</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Last Update Time:</p></td>
                                    </tr>
                                    <tr>
                                     <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        ${(variables.hotelSystemAmountBeforeTax != null && variables.hotelSystemAmountAfterTax != null)
                                         ?taxBoth:taxAnyone}</p>
                                        </td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        ${variables.lastModifyDate ?: '-'}</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Daily Rates:</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Comments:</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        ${dailyRatePrettyStr}
                                        </p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        ${variables.comments?.findAll { StringUtils.isNotBlank(it) }?.join(',') ?: '-'}</p></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:680px;display: inline-table;padding-bottom: 8px"
                           border="0">
                        <tr>
                            <td style="text-align: center; padding-bottom: 16px;">
                                <table role="presentation" cellpadding="0" cellspacing="0"
                                       style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                       border="0">
                                    <tr>
                                        <td colspan="2"><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">ROOM RATE</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Room:</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px; font-weight: bold; color: #999999;">Room Count:</p></td>
                                    </tr>
                                     <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        [${variables.roomRates != null && variables.roomRates.size() > 0 ? variables.roomRates[0].roomId ?: '-' : '-'}] ${variables.roomRates != null && variables.roomRates.size() > 0 ? variables.roomRates[0].roomName ?: '-' : '-'}</p>
                                        </td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        ${variables.roomCount ?: '-'}</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Rate ID:</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 12px;margin:14px 0 4px;  font-weight: bold; color: #999999;">Guest Count:</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        ${variables.roomRates != null && variables.roomRates.size() > 0 ? variables.roomRates[0].rateId ?: '-' : '-'}</p>
                                        </td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:0;">
                                        Adults: ${variables.adultCount ?: '-'}<br/>Childs: ${variables.childCount ?: '-'}</p></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:680px;display: inline-table;padding-bottom: 8px"
                           border="0">

                        <tr>
                            <td style="text-align: center; padding-bottom: 16px;">
                                <table role="presentation" cellpadding="0" cellspacing="0"
                                       style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                       border="0">
                                    <tr>
                                        <td colspan="3"><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">CONTACT PERSON</p></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 30%;"><p style="font-size: 14px;margin:14px 0 4px; font-weight: bold;">
                                            ${variables.contactPerson ? Joiner.on(" ").join(variables.contactPerson.firstName, variables.contactPerson.lastName) ?: '-' : '-'}
                                            </p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:14px 0 4px;">
                                            ${variables.contactPerson?.email ?: '-'}</p></td>
                                        <td style="width: 20%;"><p style="font-size: 14px;margin:14px 0 4px;">
                                            ${variables.contactPerson?.phone ?: '-'}</p></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <tr>
                <td style="text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0"
                           style="background:#fff;font-size:0px;width:680px;display: inline-table;padding-bottom: 8px"
                           border="0">

                        <tr>
                            <td style="text-align: center; padding-bottom: 16px;">
                                <table role="presentation" cellpadding="0" cellspacing="0"
                                       style="box-sizing: border-box;background:#fff;font-size:0px;padding: 0 24px;width: 100%;display: inline-table;text-align: left;"
                                       border="0">
                                    <tr>
                                        <td colspan="3"><p style="font-size: 14px;margin: 0; padding: 16px 0 8px;color: #999999; font-weight: bold;border-bottom: 1px solid #ccc;">GUEST</p></td>
                                    </tr>
                                    ${variables.guests?.withIndex()?.collect { guest, index ->
                                    """
                                    <tr>
                                        <td style="width: 30%;"><p style="font-size: 14px;margin:14px 0 4px; font-weight: bold;">
                                            ${Joiner.on(" ").join(guest?.firstName, guest?.lastName) ?: '-'}</p></td>
                                        <td style="width: 50%;vertical-align: text-top;"><p style="font-size: 14px;margin:14px 0 4px;">
                                            ${guest?.email ?: '-'}</p></td>
                                        <td style="width: 20%;"><p style="font-size: 14px;margin:14px 0 4px;">
                                            ${guest?.phone ?: '-'}</p></td>
                                    </tr>
                                        """
                                    }?.join('') ?: ''
                                }
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>

    </body>
</html>
"""
    return template
}