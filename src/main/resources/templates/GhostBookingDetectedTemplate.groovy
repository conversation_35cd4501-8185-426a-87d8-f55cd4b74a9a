

String fillingTemplate_EN(Map<String, Object> variables) {
    def template = """
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>[Attention!!] Failed to confirm the following booking to the guest</title>
  <style type="text/css">
    body {
      width: 100% !important;
      height: 100% !important;
      margin: 0;
      padding: 0;
    }

    table {
      color: #666;
      border-collapse: collapse !important;
    }

    table a {
      color: #666;
      text-decoration: none !important;
    }

    table .customizeLink a {
      color: #0d7ee0 !important;
    }
  </style>
</head>
<body>
  <table align="center" style="background: #f8f9ff; width: 100%; margin: 0 auto">
    <tr style="background: #f8f9ff">
      <td style="padding: 0">
        <table align="center" width="800" style="border-radius: 4px; margin: 0 auto" border="0" cellpadding="0" cellspacing="0">
          <!-- Header -->
          <tr>
            <td style="text-align: center">
              <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="font-size: 0; width: 800px; background-color: #fff; border-top-left-radius: 24px; border-top-right-radius: 24px; margin-top: 16px" border="0">
                <tbody>
                  <tr>
                    <td>
                      <div align="center" role="presentation" style="font-size: 0; width: 720px; background-color: #efa610; border-radius: 6px; margin: 36px auto; display: flex; justify-content: flex-start" border="0">
                        <div style="display: flex; align-items: center; padding-left: 24px; text-align: left">
                          <img src="${variables.consoleUrl}/public/images/icon-noticeV2.png" style="border: none; border-radius: 0; display: block; outline: none; text-decoration: none; width: 100%; height: auto" alt="Notice Icon">
                        </div>
                        <div style="padding: 24px 0; text-align: left; margin-left: 16px">
                          <p style="font-size: 16px; margin: 0; text-align: left; color: #fff; font-weight: 600">Important Notice</p>
                        </div>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>

          <tr>
            <td style="text-align: center">
              <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="font-size: 0; width: 800px; background-color: #fff; border-top-left-radius: 4px; border-top-right-radius: 4px" border="0">
                <tbody>
                  <tr>
                    <td style="padding: 30px 48px; text-align: left">
                      <img style="border: none; border-radius: 0; outline: none; text-decoration: none; width: 256px; height: auto; display: inline-block; vertical-align: middle" src="${variables.consoleUrl}/public/images/logoColorV2.png" alt="Logo">
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>

          <tr>
            <td style="text-align: center">
              <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="width: 800px; height: 160px" border="0">
                <tbody>
                  <tr>
                    <td style="text-align: center; width: 800px; height: 160px; background-color: #fff">
                      <p style="font-size: 48px; font-weight: bold; text-align: left; color: #00131c; background: #fff; margin: 36px 48px">[Attention!!] Failed to confirm the following booking to the guest</p>
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>

          <!-- Content -->
          <tr>
            <td>
              <table align="center" style="background: #fff; font-size: 0; width: 800px; border-bottom-left-radius: 24px; border-bottom-right-radius: 24px">
                <tr>
                  <td style="display: inline-block">
                    <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="background: #fff; font-size: 0; display: inline-block; margin: 0 48px 36px; border-bottom-left-radius: 24px; border-bottom-right-radius: 24px" border="0">
                      <tr>
                        <td style="font-size: 14px" align="left">
                          <span>Dear Customer,</span>
                        </td>
                      </tr>
                      <tr>
                        <td style="padding-top: 36px; font-size: 14px" align="left">
                          It is detected that the channel failed to receive the response of the below booking request in time. We would like to warn you that there exist possibilities that the booking in your PMS was not confirmed to the guest, which might cause an unpaid No-show.
                        </td>
                      </tr>
                      <tr>
                        <td style="padding-top: 36px; font-size: 14px" align="left">
                          Channel: [${variables.channelName}]
                        </td>
                      </tr>
                      <tr>
                        <td style="font-size: 14px" align="left">
                          Booking ID: [${variables.channelResId}]
                        </td>
                      </tr>
                      <tr>
                        <td style="font-size: 14px" align="left">
                          PMS Booking ID: [${variables.hotelSystemResId}]
                        </td>
                      </tr>
                      <tr>
                        <td style="font-size: 14px" align="left">
                          Booking Time: [${variables.createDate}]
                        </td>
                      </tr>
                      <tr>
                        <td style="padding-top: 36px; font-size: 14px" align="left">
                          Please check with the channel for the actual status of the booking, and you can cancel the booking in your PMS if it is not confirmed.
                        </td>
                      </tr>
                      <tr>
                        <td style="padding-top: 36px; font-size: 14px">
                          <a href="${variables.consoleUrl}/widget/reservation/detail?derbyResIdNo=${variables.derbyResId}&propertyId=${variables.hotelId}" style="display: block; width: 304px; height: 40px; border-radius: 4px; background: #00131c; color: white !important; line-height: 40px; text-align: center; text-decoration: none">View Reservation on Property Connector</a>
                        </td>
                      </tr>
                      <tr>
                        <td style="padding-top: 40px; font-size: 14px" align="left">
                          For any questions, please send the email to
                        </td>
                      </tr>
                      <tr>
                        <td style="font-size: 14px" align="left" class="customizeLink">
                          <span><EMAIL>.</span>
                        </td>
                      </tr>
                      <tr>
                        <td>
                          <p style="font-size: 14px; margin: 36px 0 0">Thanks,</p>
                        </td>
                      </tr>
                      <tr>
                        <td>
                          <p style="font-size: 14px; margin: 0">The DerbySoft Property Connector Team</p>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </td>
          </tr>

          <!-- Footer -->
          <tr>
            <td style="text-align: center; padding: 0">
              <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="font-size: 0; width: 800px; height: 260px; display: inline-table; border-bottom-left-radius: 4px; border-bottom-right-radius: 4px; margin-bottom: 16px" border="0">
                <tbody>
                  <tr>
                    <td style="text-align: center; padding: 0">
                      <a style="width: 110px; margin: 32px auto 0; display: inline-block" href="https://derbysoft.com">
                        <img src="${variables.consoleUrl}/public/images/icon-derbysoftV2.png" style="border: none; border-radius: 0; display: block; outline: none; text-decoration: none; width: 100%; height: auto" alt="DerbySoft">
                      </a>
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 0">
                      <p style="width: 430px; font-size: 12px; margin: 0 auto 18px; text-align: center; color: #999">DALLAS | SHANGHAI | TOKYO | BARCELONA</p>
                      <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="width: 800px; height: 32px; display: inline-table; margin-bottom: 18px" border="0">
                        <tbody style="text-align: center">
                          <tr>
                            <td>
                              <a style="width: 32px; display: inline-block; margin-right: 24px" href="https://www.facebook.com/derbysoft" target="_blank">
                                <img src="${variables.consoleUrl}/public/images/icon-facebookV2.png" style="border: none; border-radius: 0; display: block; outline: none; text-decoration: none; width: 100%; height: auto" alt="Facebook">
                              </a>
                              <a style="width: 32px; display: inline-block; margin-right: 24px" href="https://www.linkedin.com/company/derbysoft/" target="_blank">
                                <img src="${variables.consoleUrl}/public/images/icon-linkedinV2.png" style="border: none; border-radius: 0; display: block; outline: none; text-decoration: none; width: 100%; height: auto" alt="LinkedIn">
                              </a>
                              <a style="width: 32px; display: inline-block" href="https://twitter.com/derbysoft" target="_blank">
                                <img src="${variables.consoleUrl}/public/images/icon-twitterV2.png" style="border: none; border-radius: 0; display: block; outline: none; text-decoration: none; width: 100%; height: auto" alt="Twitter">
                              </a>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <p style="width: 430px; font-size: 12px; margin: 0 auto; text-align: center; color: #999">Dallas Headquarters:</p>
                      <p style="width: 430px; font-size: 12px; margin: 0 auto 8px; text-align: center; color: #999">14800 Landmark Blvd, Suite 640, Dallas TX 75254</p>
                      <p style="width: 430px; font-size: 12px; margin: 0 auto 8px; text-align: center; color: #999">© 2002 - 2024 DerbySoft Ltd. All rights reserved.</p>
                      <p style="width: 430px; font-size: 16px; margin: 0 auto 8px; text-align: center; color: #0d7ee0; font-weight: 600">
                        <a href="https://www.derbysoft.com/privacy-policy" style="color: #0d7ee0">Privacy Policy</a>
                      </p>
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>
"""

    return template
}
