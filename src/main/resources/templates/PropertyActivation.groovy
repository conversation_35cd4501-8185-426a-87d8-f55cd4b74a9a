package templates

String fillingTemplate_EN(Map<String, Object> variables) {
    def template = """
<!DOCTYPE html>
<html>
<head>
  <title>Notice of Property Activation</title>
  <meta />
  <meta name="viewport" content="width=device-width" />
  <meta />
</head>
<body style="width: 100% !important; height: 100% !important; margin: 0; padding: 0;">
  <table align="center" style="background: #f8f9ff; width: 100%; margin: 0 auto; color: #666; border-collapse: collapse !important;">
    <tr style="background: #f8f9ff">
      <td style="padding: 0">
        <table align="center" width="800px" style="border-radius: 4px; margin: 0 auto;" border="0" cellpadding="0" cellspacing="0">
          
          <!-- Header start -->
          <tr>
            <td style="text-align: center">
              <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="font-size: 0px; width: 800px; background-color: #fff; border-top-left-radius: 4px; border-top-right-radius: 4px; margin-top: 16px;" border="0">
                <tr>
                  <td style="padding: 30px 48px; text-align: left">
                    <img src="https://pc.derbysoftsec.com/public/images/logoColor.png" alt="" style="border: none; outline: none; text-decoration: none; width: 256px; height: auto; display: inline-block; vertical-align: middle;" />
                  </td>
                </tr>
              </table>
            </td>
          </tr>

          <tr>
            <td style="text-align: center">
              <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="width: 800px; height: 160px; display: inline-table;" border="0">
                <tr>
                  <td style="text-align: center; width: 800px; height: 160px; background-color: #004771;">
                    <p style="color: #fff; font-size: 24px; line-height: 30px; font-weight: bold; text-align: center;">Notice of Property Activation</p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <!-- Header end -->

          <!-- Content start -->
          <tr>
            <td>
              <table align="center" style="background: #fff; font-size: 0px; width: 800px; display: inline-table;" border="0">
                <tr>
                  <td style="display: inline-block">
                    <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="background: #fff; font-size: 0px; width: 100%; display: inline-block; padding: 36px 48px 90px;" border="0">
                      
                      <tr valign="top">
                        <td valign="top" style="font-size: 14px;" align="left">
                          <span>Hi Customer Success Team,</span>
                        </td>
                      </tr>

                      <tr valign="top">
                        <td valign="top" style="font-size: 14px;" align="left">
                          <p style="margin: 0; margin-top: 24px; line-height: 24px; font-size: 14px;">
                            <b>[##propertyId##]##propertyName##</b> lived.<br />
                            You could click the following button to check the details
                          </p>
                        </td>
                      </tr>

                      <tr>
                        <td style="text-decoration: none; padding-top: 24px; font-size: 14px;">
                          <a href="##consoleUrl##/widget/administration/propertyGroupList" style="display: block; width: 306px; height: 40px; border-radius: 4px; background: #004771; color: white !important; line-height: 40px; text-align: center; font-weight: 600; text-decoration: none;">
                            Open Property Connector Administration
                          </a>
                        </td>
                      </tr>

                      <tr valign="top">
                        <td valign="top" style="font-size: 14px;" align="left">
                          <p style="margin: 0; margin-top: 24px; line-height: 24px; font-size: 14px;">
                            For any questions, please send the email to
                            <a style="color: #666 !important; text-decoration: none !important;" href="mailto:<EMAIL>"><EMAIL></a>
                          </p>
                        </td>
                      </tr>

                      <tr valign="top">
                        <td valign="top" style="font-size: 14px;" align="left">
                          <p style="margin: 0; margin-top: 24px; line-height: 24px; font-size: 14px;">
                            Thanks,<br />
                            <span>The DerbySoft Property Connector Team</span>
                          </p>
                        </td>
                      </tr>

                    </table>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <!-- Content end -->

          <!-- Footer start -->
          <tr>
            <td style="text-align: center; padding: 0">
              <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="background: #fff; font-size: 0px; width: 800px; height: 260px; display: inline-table; border-top: 1px solid #eeeff0; border-bottom-left-radius: 4px; border-bottom-right-radius: 4px; margin-bottom: 16px;" border="0">
                <tr>
                  <td style="text-align: center; padding: 0">
                    <a href="https://derbysoft.com" style="width: 110px; margin: 32px auto 0; display: inline-block; color: #666 !important; text-decoration: none !important;">
                      <img src="https://pc.derbysoftsec.com/public/images/icon-derbysoft.png" style="border: none; display: block; outline: none; text-decoration: none; width: 100%; height: auto;" alt="DerbySoft" />
                    </a>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 0">
                    <p style="width: 430px; font-size: 12px; margin: 0 auto 18px; text-align: center; color: #999;">
                      DALLAS | SHANGHAI | TOKYO | BARCELONA
                    </p>
                    <table align="center" role="presentation" cellpadding="0" cellspacing="0" style="width: 800px; height: 32px; display: inline-table; margin-bottom: 18px;" border="0">
                      <tr>
                        <td style="text-align: center">
                          <a href="https://www.facebook.com/derbysoft" target="_blank" style="width: 32px; display: inline-block; margin-right: 24px;">
                            <img src="https://pc.derbysoftsec.com/public/images/icon-facebook.png" style="width: 100%; height: auto;" />
                          </a>
                          <a href="https://www.linkedin.com/company/derbysoft/" target="_blank" style="width: 32px; display: inline-block; margin-right: 24px;">
                            <img src="https://pc.derbysoftsec.com/public/images/icon-linkedin.png" style="width: 100%; height: auto;" />
                          </a>
                          <a href="https://twitter.com/derbysoft" target="_blank" style="width: 32px; display: inline-block;">
                            <img src="https://pc.derbysoftsec.com/public/images/icon-twitter.png" style="width: 100%; height: auto;" />
                          </a>
                        </td>
                      </tr>
                    </table>
                    <p style="width: 430px; font-size: 12px; margin: 0 auto; text-align: center; color: #999;">
                      Dallas Headquarters:
                    </p>
                    <p style="width: 430px; font-size: 12px; margin: 0 auto 8px; text-align: center; color: #999;">
                      14800 Landmark Blvd, Suite 640, Dallas TX 75254
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <!-- Footer end -->

        </table>
      </td>
    </tr>
  </table>
</body>
</html>

"""
    template = template.replaceAll(/##(.+?)##/) {
        variables[it[1]]
    }

    return template
}