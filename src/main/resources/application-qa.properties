configkeeper.url=service-registry-default.stone:10080
configkeeper.source=pcmsc:us-west-2:next:::
#service.group=qa

## log
logger.ROOT=INFO
logger.perf=INFO
logger.action=INFO
logger.http.AccessLog =INFO
logger.http.StreamLog=INFO

log.file.ROOT.path = /usr/local/logs/pcmsc.log
log.file.perf.path = /usr/local/logs/pcmsc.perf_v2.log
log.file.action.path = /usr/local/logs/pcmsc.action.log
log.file.http.AccessLog.path = /usr/local/logs/pcmsc.http.access.log
log.file.http.StreamLog.path = /usr/local/logs/pcmsc.streamindex_v2.log

log.appender.ROOT = file
log.appender.perf = file
log.appender.action = file
log.appender.http.AccessLog = file
log.appender.http.StreamLog = file


# sla configs
sla.service.id.list = mongo
sla.service.mongo.type = mongo
sla.service.mongo.service = pcmsc-MongoDB

# dtrace
next.dtrace.service-name = PCCM
next.dtrace.endpoint = http://dtrace-collector-default.stone:14268/api/traces
next.dtrace.reporter-log-spans = false
next.dtrace.sampler-type = probabilistic
next.dtrace.sampler-param = 0.1
next.dtrace.reporter-max-queue-size = 100000
next.dtrace.reporter-max-packet-size = 1048576
next.dtrace.reporter-flush-interval = 1000