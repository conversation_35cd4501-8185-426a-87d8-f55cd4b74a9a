package com.derbysoft.next.sample.dto.enums;

/**
 * <AUTHOR>
 */
public enum MailStatus {

    Received("received"),
    Sends("sends"),
    Failure("failure"),
    RenderingFailure("renderingFailure");

    private String value;

    private MailStatus(String value) {
        this.value = value;
    }

    public String toString() {
        return this.value;
    }

    public static MailStatus fromValue(String value) {
        if (value != null && !"".equals(value)) {
            MailStatus[] var1 = values();
            int var2 = var1.length;

            for (int var3 = 0; var3 < var2; ++var3) {
                MailStatus enumEntry = var1[var3];
                if (enumEntry.toString().equals(value)) {
                    return enumEntry;
                }
            }
            throw new IllegalArgumentException("Cannot create enum from " + value + " value!");
        } else {
            throw new IllegalArgumentException("Value cannot be null or empty!");
        }
    }
}
