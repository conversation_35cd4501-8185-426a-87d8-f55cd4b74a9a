package com.derbysoft.next.sample.dto.profile;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@Setter
@Getter
@Builder
public class RatePlan {

    private String rateId;
    private String rateName;
    private String currency;
    private String status;
    private String paymentType;
    private String guaranteeType;
    private String rateType;
    private String priceType;
    private List<CancelPolicyWithDateRange> cancelPolicies;
    private String defaultMealPlan;
    private Map<String, Object> extension;
    private BigDecimal minPrice;
    private BigDecimal maxPrice;
    private Boolean isVirtual;
    private String hotelSystemRateId;
    private List<String> roomIds;
    private Boolean isDynamic;
    private String rateCategory;
}
