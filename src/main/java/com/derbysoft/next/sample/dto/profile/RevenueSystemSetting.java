package com.derbysoft.next.sample.dto.profile;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

@Data
@Builder
public class RevenueSystemSetting {


    private String hotelSystemConnectionId;
    private String hotelSystemHotelId;
    private String hotelSystemChainId;
    private String connectorType; // ALL/ARI/RES
    private String rateType; // AmountBeforeTax/AmountAfterTax/Both
    private String category; // RMS
    private Map<String, String> creditCardMapping;
    private Map<String, Object> setting;


}
