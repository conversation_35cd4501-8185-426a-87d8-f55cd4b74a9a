package com.derbysoft.next.sample.dto.profile;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class HotelSystemChannelInfo {

    private String hotelId;
    private String hotelName;
    private String timezone;
    private String defaultLanguage;
    private String status;
    private String region;
    private String reservationType; // ZeroWay|TwoWay null->TwoWay
    private String modifyType;
    private Boolean desensitizeFlg;
    private List<RoomType> roomTypes;
    private List<RoomClass> roomClasses;
    private List<RatePlan> ratePlans;
    private List<RateClass> rateClasses;
    private List<Product> products;
    private Map<String, List<String>> notifyMails;

    private HotelChannelMappingDTO hotelChannelMappingDTO;
    private HotelConnectionDTO hotelConnectionDTO;
    private ChannelDTO channelDTO;
    private HotelSystemInstanceDTO hotelSystemInstanceDTO;
    private List<HotelSystemConnectionDTO> hotelSystemConnectionDTOs;
    private List<CrsConnectionDTO> crsConnectionDTOs;
    private List<HotelSystemMappingDTO> hotelSystemMappingDTOs;
}
