package com.derbysoft.next.sample.dto.profile;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;


@Getter
@Setter
public class ChannelDTO {

    private String channelId;
    private Map<String, String> nameMultiLang;
    private String status;
    private Boolean defaultMapping;
    private String category;
    private Map<String, String> descMultiLang;
    private String ariSource; // EBK/PMS
    private Boolean supportPromo;
    private Boolean resRetryFlg;
    private Boolean availCheckFlg;
    private Boolean connected;
    private Integer resChannelTimeOut;
    private Integer sort;
    private Map<String, Object> settings;
    private String rateType;
    private Boolean supportCorp;
    private List<String> categories;
    private List<String> regions;
    private List<String> tags;

}
