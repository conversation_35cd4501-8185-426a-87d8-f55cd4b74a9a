package com.derbysoft.next.sample.dto.profile;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class CrsConnectionDTO {

    private String hotelSystemConnectionId;
    private String hotelSystemId;
    private String hotelSystemName;
    private String apiType;
    private String ariType;
    private Boolean allowUpdRestriction;
    private Boolean supportModify;
    private String hotelSystemTransactionType;
    private String hotelSystemVer;
    private String connectionMode;
    private String ariConnectionMode;
    private String resConnectionMode;
    private Boolean isNotifyMode;
    private Integer sort;
    private Map<String, String> creditCardMapping;
    private Map<String, Object> settings;
    private String status;
    private Boolean virtualFlg;
    private Boolean dynamicFlg;
    private String region;
    private String category;
    private List<String> channels;
    private Map<String, Object> channelExtensions;
    private Boolean ackRequired;

}
