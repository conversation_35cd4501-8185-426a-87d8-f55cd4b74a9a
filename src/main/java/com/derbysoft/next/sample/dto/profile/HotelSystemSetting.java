package com.derbysoft.next.sample.dto.profile;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@Builder
public class HotelSystemSetting {

    private String hotelSystemConnectionId;
    private String hotelSystemHotelId;
    private String hotelSystemChainId;
    private String connectorType; // ALL/ARI/RES
    private String pmsRateType; // AmountBeforeTax/AmountAfterTax/Both
    private String category; // PMS
    private Map<String, String> creditCardMapping;
    private Map<String, Object> setting;

}
