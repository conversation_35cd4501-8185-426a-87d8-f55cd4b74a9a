package com.derbysoft.next.sample.dto.profile;

import com.google.common.base.Objects;
import lombok.*;
import org.joda.time.LocalDate;

import jakarta.validation.constraints.NotNull;

@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DateRange {

    @NotNull
    private LocalDate startDate;

    @NotNull
    private LocalDate endDate;

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof DateRange) {
            DateRange item = (DateRange) obj;
            return Objects.equal(item.startDate, this.startDate) &&
                    Objects.equal(item.endDate, this.endDate);
        }
        return false;
    }

}
