package com.derbysoft.next.sample.dto.profile;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

@Getter
@Setter
@FieldNameConstants(innerTypeName = "CONSTANT")
public class RateRule {
    private String channelRateType;
    private String channelPriceType;
    private String channelResRateType;
    private String channelResPriceType;

    private String rateType;
    private String resRateType;

}
