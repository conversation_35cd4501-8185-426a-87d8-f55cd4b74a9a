package com.derbysoft.next.sample.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Builder
public class SendEmailMessage {

    private String messageId;
    private String mailFrom;
    private String[] mailTo;
    private String[] mailCc;
    private String[] mailBcc;
    private String mailSubject;
    private String mailContent;

    // TODO 附件临时实现方案，后续可能根据正式方案进行替换
    private List<MultipartFile> attachments;

}
