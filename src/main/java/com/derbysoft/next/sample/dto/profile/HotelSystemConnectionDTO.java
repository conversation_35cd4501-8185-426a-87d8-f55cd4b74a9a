package com.derbysoft.next.sample.dto.profile;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class HotelSystemConnectionDTO {

    private String hotelSystemConnectionId;
    private String hotelSystemId;
    private String hotelSystemName;
    private String apiType;
    private String ariType;
    private Boolean allowUpdRestriction;
    private Boolean supportModify;
    private String hotelSystemTransactionType; // UUID/SEQUENCE/DERBY_RES_ID
    private String hotelSystemVer;
    private String connectionMode; // PUSH|PULL
    private Boolean isNotifyMode;
    private Integer sort;
    private Map<String, String> creditCardMapping;
    private Map<String, Object> settings;
    private String status;
    private String region;
    private Boolean ackRequired;

}
