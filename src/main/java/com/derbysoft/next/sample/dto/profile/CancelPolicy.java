package com.derbysoft.next.sample.dto.profile;


import com.google.common.base.Objects;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

@Getter
@Setter
@Builder
public class CancelPolicy {

    private String code;
    private String description;

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof CancelPolicy) {
            CancelPolicy item = (CancelPolicy) obj;
            return Objects.equal(item.code, this.code) &&
                    Objects.equal(item.description, this.description);
        }
        return false;
    }

}
