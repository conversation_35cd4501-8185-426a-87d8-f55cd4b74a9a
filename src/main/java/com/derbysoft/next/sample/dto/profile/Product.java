package com.derbysoft.next.sample.dto.profile;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class Product {

    private String roomId;
    private String roomName;
    private String roomIdType;
    private String rateId;
    private String rateName;
    private String rateIdType;
    private String status;

    public String productKey() {
        return String.join("|", rateId, roomId);
    }
}
