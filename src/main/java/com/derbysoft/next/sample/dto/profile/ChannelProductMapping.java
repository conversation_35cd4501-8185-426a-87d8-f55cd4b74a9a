package com.derbysoft.next.sample.dto.profile;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class ChannelProductMapping {

    private String roomId;
    private String roomIdType;
    private String rateId;
    private String rateIdType;
    private String channelRoomId;
    private String channelRateId;
    private Boolean availStatus; // default true
    private Boolean calcFlg; // null -> true
    private String status;
    private Map<String, Object> extension;
    private List<String> suitMemberLevels;
}
