package com.derbysoft.next.sample.dto.profile;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;


@Getter
@Setter
public class HotelConnectionDTO {

    private String hotelId;
    private String connectionManagerId;
    private String channelId;
    private String channelHotelId;
    private String status;

    private String ariLevel;
    private String provider;
    private Boolean showMealPlans;

    private Map<String, Object> hotelSystemSettings;
    private Map<String, Object> setupInfos;
    private Map<String, Object> channelSettings;

    private String currency;
    private RateRule rateRule;

    private String resTaxRule; // AmountBeforeTax、AmountAfterTax、ByRatePlan

    private List<String> commentsType; // ['Promotions','Coupons']

    private Integer resChannelTimeOut;
    private String iata;

    private String connectorType;//ARI RES ALL
    /**
     * null or 小于等于0 表示OccupancyRate
     * 大于0 表示CommonRate 具体数字代表使用几人价
     */
    private Integer rateForm;
    private Boolean isDerbyConfirm;
    private String modifyType;
    private Boolean availCheckFlg;
    private String goLiveDate;
}
