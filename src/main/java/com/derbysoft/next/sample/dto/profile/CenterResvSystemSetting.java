package com.derbysoft.next.sample.dto.profile;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class CenterResvSystemSetting {

    private List<CrsHotelChannel> hotelSystemChannels;
    private String connectorType; // ALL/ARI/RES
    private String category; //CRS
    private Map<String, String> creditCardMapping;
    private Map<String, Object> setting;

    @Data
    @Builder
    public static class CrsHotelChannel {
        private String hotelSystemConnectionId;
        private String hotelSystemHotelId;
        private String hotelSystemChainId;
        private String channelId;
        private String rateType; // AmountBeforeTax/AmountAfterTax/Both
    }
}
