package com.derbysoft.next.sample.client;

import com.derbysoft.next.sample.dto.AccessTokenConfigDTO;
import com.derbysoft.next.sample.dto.JWTInfo;
import com.derbysoft.next.sample.dto.profile.ChannelDTO;
import com.derbysoft.next.sample.dto.profile.HotelSystemChannelInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @author: cy.wang
 */
//@FeignClient(name = "PCCM", url = "https://pcendpoint.derbysoft-test.com/pcapigateway/pccm")
@FeignClient(name = "PCCM", path = "/pccm")
public interface PcCMFeignClient {

    @PostMapping(value = "/accessTokens/generate",
            headers = {"X-Next-TransactionType=Legacy", "X-Next-ServiceName=generateAccessToken"})
    AccessTokenConfigDTO generateAccessToken(@RequestBody JWTInfo request);
}
