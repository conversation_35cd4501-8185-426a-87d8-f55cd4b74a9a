package com.derbysoft.next.sample.client;

import com.derbysoft.next.sample.dto.profile.ChannelDTO;
import com.derbysoft.next.sample.dto.profile.HotelSystemChannelInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: cy.wang
 */
//@FeignClient(name = "PCPROFILE", url = "http://localhost:8082/pcprofile", configuration = ProfileClientConfig.class)
//@FeignClient(name = "PCPROFILE", url = "https://pcendpoint.derbysoft-test.com/pcapigateway/pcprofile", configuration = ProfileClientConfig.class)
//@FeignClient(name = "PCPROFILE", url = "https://pc-rt.derbysoft-test.com/pcapigateway/pcprofile", configuration = ProfileClientConfig.class)
@FeignClient(name = "PCPROFILE", path = "/pcprofile", configuration = ProfileClientConfig.class)
public interface PcProfileFeignClient {

    @GetMapping(value = "/api/hotel/detail",
            headers = {"X-Next-TransactionType=Legacy", "X-Next-ServiceName=hotelDetail", "X-Cache-Mode=cache"})
    HotelSystemChannelInfo findAirbnbHotelDetailByListingId(@RequestParam(name = "channelId") String channelId,
                                                            @RequestParam(name = "channelRoomId") String channelRoomId);

    @GetMapping(value = "/api/channels/{channelId}",
            headers = {"X-Next-TransactionType=Legacy", "X-Next-ServiceName=channel", "X-Cache-Mode=cache"})
    ChannelDTO findChannel(@PathVariable(name = "channelId") String channelId);
}
