package com.derbysoft.next.sample.client;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Value;

public class ProfileInterceptor implements RequestInterceptor {

    @Value("${spring.application.name:}")
    private String referer;

    @Override
    public void apply(RequestTemplate template) {
        template.header("next-referer", referer);
    }
}
