package com.derbysoft.next.sample.controller;

import com.derbysoft.next.sample.dto.SearchEmailRecordsRequest;
import com.derbysoft.next.sample.dto.SearchEmailRecordsResponse;
import com.derbysoft.next.sample.service.EmailRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
public class EmailRecordController {

    private EmailRecordService emailRecordService;

    @PostMapping(value = "/searchEmailRecords")
    public SearchEmailRecordsResponse queryEmailSendLogs(@RequestBody SearchEmailRecordsRequest request,
                                                         @RequestParam(value = "page", defaultValue = "0") Integer page,
                                                         @RequestParam(value = "size", defaultValue = "10") Integer size) {
        request.setPage(page);
        request.setSize(size);
        return emailRecordService.searchEmailRecords(request);
    }

    @Autowired
    public void setEmailRecordService(EmailRecordService emailRecordService) {
        this.emailRecordService = emailRecordService;
    }

}
