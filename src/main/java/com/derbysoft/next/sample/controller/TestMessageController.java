package com.derbysoft.next.sample.controller;

import com.derbysoft.next.commons.core.perf.PerfTracer;
import com.derbysoft.next.commons.core.perf.annotation.Perf;
import com.derbysoft.next.sample.domain.entity.MessageRecordEntity;
import com.derbysoft.next.sample.service.MessageRecordService;
import com.derbysoft.next.sample.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/test")
@Slf4j
public class TestMessageController {

    @Autowired
    private MessageRecordService messageRecordService;

    @GetMapping(value = "/send/message/{derbyMessageId}")
    @Perf(operation = Constants.Perf.IGNORE)
    public MessageRecordEntity RetrieveMessage(@PathVariable String derbyMessageId) {
        PerfTracer.get().parameter(Constants.PerfField.EXT + "Dev_retrieveMessage", String.valueOf(true));
        return messageRecordService.findMessageRecordByDerbyMessageId(derbyMessageId);
    }

    @PostMapping(value = "/parse/message/{derbyMessageId}")
    @Perf(operation = Constants.Perf.IGNORE)
    public String parseMessage(@PathVariable String derbyMessageId) {
        PerfTracer.get().parameter(Constants.PerfField.EXT + "Dev_parseMessage", String.valueOf(true));
        messageRecordService.parseAndProcessMessage(derbyMessageId);
        return "successful";
    }

    @PostMapping(value = "/send/message/{derbyMessageId}")
    @Perf(operation = Constants.Perf.IGNORE)
    public String sendMessage(@PathVariable String derbyMessageId) {
        PerfTracer.get().parameter(Constants.PerfField.EXT + "Dev_sendMessage", String.valueOf(true));
        messageRecordService.processMessageRecord(derbyMessageId);
        return "successful";
    }
}
