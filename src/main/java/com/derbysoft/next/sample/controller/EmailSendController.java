package com.derbysoft.next.sample.controller;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ZipUtil;
import com.derbysoft.next.commons.core.exception.ServiceException;
import com.derbysoft.next.commons.core.perf.PerfTracer;
import com.derbysoft.next.commons.core.perf.annotation.Perf;
import com.derbysoft.next.commons.util.id.IdGenerator;
import com.derbysoft.next.sample.domain.entity.EmailRecordEntity;
import com.derbysoft.next.sample.dto.EmailRetryRequest;
import com.derbysoft.next.sample.dto.SendRawEmailRequest;
import com.derbysoft.next.sample.dto.SendTemplatedEmailRequest;
import com.derbysoft.next.sample.dto.enums.MailStatus;
import com.derbysoft.next.sample.dto.enums.MailTemplateType;
import com.derbysoft.next.sample.dto.enums.MessageType;
import com.derbysoft.next.sample.kafka.KafkaConfiguration;
import com.derbysoft.next.sample.kafka.producer.KafkaMessageProducer;
import com.derbysoft.next.sample.mail.MailGroupHelper;
import com.derbysoft.next.sample.service.EmailRecordService;
import com.derbysoft.next.sample.service.EmailRetryService;
import com.derbysoft.next.sample.service.MailServerService;
import com.derbysoft.next.sample.util.Constants;
import com.google.gson.Gson;
import org.jsoup.Jsoup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.springframework.util.Assert.*;

/**
 * <AUTHOR>
 */
@RestController
public class EmailSendController {

    private static final Logger LOGGER = LoggerFactory.getLogger(EmailSendController.class);

    private KafkaMessageProducer kafkaMessageProducer;
    private KafkaConfiguration kafkaConfiguration;

    private Gson gson;
    private IdGenerator<Long> idWorker;

    private EmailRecordService emailRecordService;

    private EmailRetryService emailRetryService;

    private MailServerService mailServerService;

    // 20240524 add by andrew.yang 6593159538
    private MailGroupHelper mailGroupHelper;

    // 20240306 add by yc 6033290326 start
    @Value("${mail.template.console.url:}")
    private String mailTemplateConsoleUrl;
    // 20240306 add by yc 6033290326 end


    @PostMapping(value = "/sendRawEmail")
    @Perf(operation = Constants.Perf.PRODUCE_MESSAGE)
    public String sendRawEmail(@RequestBody SendRawEmailRequest sendRawEmailRequest) {
        this.checkParam(sendRawEmailRequest);

        String groupMessageId = null;
        try {
            groupMessageId = Optional.ofNullable(sendRawEmailRequest.getMessageId()).orElse(this.getMessageId());
            List<MailGroupHelper.MailGroup> groups = this.mailGroupHelper.groupWithDistribution(
                    sendRawEmailRequest.getMailTo(),
                    sendRawEmailRequest.getMailCc(),
                    sendRawEmailRequest.getMailBcc());

            int index = 1;
            PerfTracer.get().parameter(Constants.PerfField.MESSAGE_ID_DERBY, groupMessageId);
            PerfTracer.get().parameter(Constants.PerfField.MESSAGE_TYPE, "sendRawEmail");

            StringBuilder childMessageIdAppender = new StringBuilder();
            for (MailGroupHelper.MailGroup group : groups) {
                String messageId = groupMessageId + "-" + index;
                EmailRecordEntity emailRecordEntity = emailRecordService.addEmailRecord(EmailRecordEntity.builder()
                        // 20240524 add by andrew.yang 6593159538 start
                        .messageGroupId(groupMessageId)
                        // 20240524 add by andrew.yang 6593159538 end
                        .messageId(messageId)
                        .messageType(MessageType.RAW.toString())
                        .mailFrom(sendRawEmailRequest.getMailFrom())
                        .mailTo(group.getMailTo())
                        .mailCc(group.getMailCc())
                        .mailBcc(group.getMailBcc())
                        .subject(sendRawEmailRequest.getSubject())
                        .content(sendRawEmailRequest.getContent())
                        .status(MailStatus.Received.toString())
                        .build());

                childMessageIdAppender.append(messageId).append(",");

                String topic = kafkaConfiguration.getRawMailMessageTopic();
                String json = gson.toJson(emailRecordEntity);
                kafkaMessageProducer.send(topic, Constants.Kafka.KAFKA_RAW_MAIL_MESSAGE_KEY + messageId, ZipUtil.gzip(json, CharsetUtil.UTF_8));
                index++;
            }
            PerfTracer.get().parameter(Constants.PerfField.CHILD_MESSAGE_ID, childMessageIdAppender.toString());
            return groupMessageId;
        } catch (DuplicateKeyException e) {
            throw new ServiceException("DuplicateKeyException", "duplicate key [messageId: " + groupMessageId + " messageType：" + MessageType.RAW + "]");
        } catch (Exception e) {
            LOGGER.error("send raw mail message to kafka error, messageId:{}", groupMessageId, e);
            throw new ServiceException("SendRawMailError", "send raw mail message error.");
        }
    }

    @PostMapping(value = "/sendTemplatedEmail")
    @Perf(operation = Constants.Perf.PRODUCE_MESSAGE)
    public String sendTemplatedEmail(@RequestBody SendTemplatedEmailRequest sendTemplatedEmailRequest) {
        this.checkParam(sendTemplatedEmailRequest);
        addExtraParameters(sendTemplatedEmailRequest);

        Map<String, Object> originalTemplateData = sendTemplatedEmailRequest.getTemplateData();

        PerfTracer.get().parameter(Constants.PerfField.HOTEL_DERBY, originalTemplateData.get("hotelId"));
        PerfTracer.get().parameter(Constants.PerfField.CHANNEL, originalTemplateData.get("channelId"));

        // 兼容html的错误信息处理
        String errorMessage = (String) originalTemplateData.get("errorMessage");
        if (errorMessage != null) {
            originalTemplateData.put("errorMessage", Jsoup.parse(errorMessage).text());
        }

        Map<String, Object> sanitizedTemplateData = requiresSanitization(originalTemplateData, sendTemplatedEmailRequest.getTemplate());

        String groupMessageId = null;
        try {
            groupMessageId = Optional.ofNullable(sendTemplatedEmailRequest.getMessageId()).orElse(this.getMessageId());
            List<MailGroupHelper.MailGroup> groups = this.mailGroupHelper.groupWithDistribution(
                    sendTemplatedEmailRequest.getMailTo(),
                    sendTemplatedEmailRequest.getMailCc(),
                    sendTemplatedEmailRequest.getMailBcc());

            int index = 1;
            PerfTracer.get().parameter(Constants.PerfField.MESSAGE_TYPE, "sendTemplatedEmail");
            PerfTracer.get().parameter(Constants.PerfField.MESSAGE_ID_DERBY, groupMessageId);

            StringBuilder childMessageIdAppender = new StringBuilder();
            for (MailGroupHelper.MailGroup group : groups) {
                String messageId = groupMessageId + "-" + index;

                EmailRecordEntity emailRecordEntity = emailRecordService.addEmailRecord(EmailRecordEntity.builder()
                        .messageGroupId(groupMessageId)
                        .messageId(messageId)
                        .messageType(MessageType.TEMPLATE.toString())
                        .mailFrom(sendTemplatedEmailRequest.getMailFrom())
                        .mailTo(group.getMailTo())
                        .mailCc(group.getMailCc())
                        .mailBcc(group.getMailBcc())
                        .subject(sendTemplatedEmailRequest.getSubject())
                        .template(sendTemplatedEmailRequest.getTemplate())
                        .templateData(sanitizedTemplateData) // 持久化使用不含敏感信息的数据
                        .status(MailStatus.Received.toString())
                        .build());

                // 推送到kafka时用原模版数据
                emailRecordEntity.setTemplateData(originalTemplateData);
                String json = gson.toJson(emailRecordEntity);

                kafkaMessageProducer.send(kafkaConfiguration.getTemplateMailMessageTopic(),
                        Constants.Kafka.KAFKA_TEMPLATE_MAIL_MESSAGE_KEY + messageId,
                        ZipUtil.gzip(json, CharsetUtil.UTF_8));

                childMessageIdAppender.append(messageId).append(",");
                index++;
            }
            PerfTracer.get().parameter(Constants.PerfField.CHILD_MESSAGE_ID, childMessageIdAppender.toString());
            return groupMessageId;
        } catch (DuplicateKeyException e) {
            throw new ServiceException("DuplicateKeyException", "duplicate key [messageId: " + groupMessageId + " messageType：" + MessageType.TEMPLATE + "]");
        } catch (Exception e) {
            LOGGER.error("send template mail message to kafka error, messageId:{}", groupMessageId, e);
            throw new ServiceException("SendTemplatedMailError", "send template mail message error.");
        }
    }

    private Map<String, Object> requiresSanitization(Map<String, Object> originalTemplateData, String template) {
        if (MailTemplateType.ReservationStatusV2.name().equals(template) || MailTemplateType.ReservationStatus.name().equals(template)) {
            Map<String, Object> sanitizedTemplateData = new HashMap<>(originalTemplateData);
            sanitizedTemplateData.remove(Constants.CONTACT_PERSON);
            sanitizedTemplateData.remove(Constants.GUESTS);
            return sanitizedTemplateData;
        }
        return originalTemplateData;
    }

    @PostMapping(value = "/emailRetry")
    public String emailRetry(@RequestBody EmailRetryRequest emailRetryRequest) {
        this.checkParam(emailRetryRequest);
        emailRetryService.emailRetry(emailRetryRequest);
        return Constants.STR_SUCCESSFUL;
    }

    @PostMapping(value = "/sendRawEmailWithAttachment", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Perf(operation = Constants.Perf.PRODUCE_MESSAGE)
    public String sendRawEmailWithAttachment(@RequestPart("mail") SendRawEmailRequest sendRawEmailRequest,
                                             @RequestPart(value = "attachments", required = false) List<MultipartFile> attachments) {
        this.checkParam(sendRawEmailRequest);
        String messageId = Optional.ofNullable(sendRawEmailRequest.getMessageId()).orElse(this.getMessageId());

        PerfTracer.get().parameter(Constants.PerfField.MESSAGE_ID_DERBY, messageId);
        PerfTracer.get().parameter(Constants.PerfField.MESSAGE_TYPE, "sendRawEmailWithAttachment");

        String senderName = mailServerService.sendRawEmailWithAttachment(sendRawEmailRequest, attachments);
        if (senderName == null) {
            throw new ServiceException("SendRawEmailWithAttachment failed", "sendRawEmail with attachment failed");
        }
        PerfTracer.get().parameter(Constants.PerfField.SUPPLIER, senderName);
        return messageId;
    }

    private void addExtraParameters(SendTemplatedEmailRequest sendTemplatedEmailRequest) {
        Map<String, Object> templateData = sendTemplatedEmailRequest.getTemplateData();
        if (templateData != null) {
            templateData.put("consoleUrl", mailTemplateConsoleUrl);
        }
    }

    private String getMessageId() {
        return String.valueOf(idWorker.generate());
    }

    private void checkParam(SendRawEmailRequest sendRawEmailRequest) {
        //hasText(sendRawEmailRequest.getMailFrom(), "missing mailFrom");
        noNullElements(sendRawEmailRequest.getMailTo(), "invalid mailTo");
        noNullElements(sendRawEmailRequest.getMailCc(), "invalid mailCc");
        noNullElements(sendRawEmailRequest.getMailBcc(), "invalid mailBcc");
        hasText(sendRawEmailRequest.getSubject(), "missing subject");
    }

    private void checkParam(SendTemplatedEmailRequest sendTemplatedEmailRequest) {
        //hasText(sendTemplatedEmailRequest.getMailFrom(), "missing mailFrom");
        noNullElements(sendTemplatedEmailRequest.getMailTo(), "invalid mailTo");
        noNullElements(sendTemplatedEmailRequest.getMailCc(), "invalid mailCc");
        noNullElements(sendTemplatedEmailRequest.getMailBcc(), "invalid mailBcc");
        hasText(sendTemplatedEmailRequest.getSubject(), "missing subject");
        hasText(sendTemplatedEmailRequest.getTemplate(), "missing template");
        notNull(sendTemplatedEmailRequest.getTemplateData(), "missing templateData");
    }

    private void checkParam(EmailRetryRequest emailRetryRequest) {
        hasText(emailRetryRequest.getMessageId(), "missing messageId");
        hasText(emailRetryRequest.getMessageType(), "missing messageType");
        hasText(emailRetryRequest.getKafkaMgmDomain(), "missing kafkaMgmDomain");
        hasText(emailRetryRequest.getKafkaMgmClusterId(), "missing kafkaMgmClusterId");
    }

    @Autowired
    public void setKafkaMessageProducer(KafkaMessageProducer kafkaMessageProducer) {
        this.kafkaMessageProducer = kafkaMessageProducer;
    }

    @Autowired
    public void setKafkaConfiguration(KafkaConfiguration kafkaConfiguration) {
        this.kafkaConfiguration = kafkaConfiguration;
    }

    @Autowired
    public void setGson(Gson gson) {
        this.gson = gson;
    }

    @Autowired
    public void setIdWorker(IdGenerator<Long> idWorker) {
        this.idWorker = idWorker;
    }

    @Autowired
    public void setEmailRecordService(EmailRecordService emailRecordService) {
        this.emailRecordService = emailRecordService;
    }

    @Autowired
    public void setEmailRetryService(EmailRetryService emailRetryService) {
        this.emailRetryService = emailRetryService;
    }

    @Autowired
    public void setMailGroupHelper(MailGroupHelper mailGroupHelper) {
        this.mailGroupHelper = mailGroupHelper;
    }

    @Autowired
    public void setMailServerService(MailServerService mailServerService) {
        this.mailServerService = mailServerService;
    }
}
