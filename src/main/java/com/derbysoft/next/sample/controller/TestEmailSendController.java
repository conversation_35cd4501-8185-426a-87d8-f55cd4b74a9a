package com.derbysoft.next.sample.controller;

import com.derbysoft.next.commons.core.exception.ServiceException;
import com.derbysoft.next.commons.core.perf.annotation.Perf;
import com.derbysoft.next.sample.dto.SendEmailMessage;
import com.derbysoft.next.sample.dto.SendRawEmailRequest;
import com.derbysoft.next.sample.mail.SesApiMailSender;
import com.derbysoft.next.sample.service.EmailRecordService;
import com.derbysoft.next.sample.util.Constants;
import com.derbysoft.next.sample.util.SendTemplateMailHelper;
import com.derbysoft.next.sample.util.SesEmailSender;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.services.ses.model.SendRawEmailResponse;

import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/test")
public class TestEmailSendController {

    private static final Logger LOGGER = LoggerFactory.getLogger(TestEmailSendController.class);

    @Autowired(required = false)
    private SesApiMailSender sesApiMailSender;

    private EmailRecordService emailRecordService;

    @PostMapping(value = "/sendRawEmail")
    public String sendRawEmail(@RequestBody SendRawEmailRequest sendRawEmailRequest) {
        SendEmailMessage message = SendEmailMessage.builder()
                .messageId(sendRawEmailRequest.getMessageId())
                .mailFrom(sendRawEmailRequest.getMailFrom())
                .mailTo(sendRawEmailRequest.getMailTo().toArray(new String[0]))
                .mailCc(noNullMailCc(sendRawEmailRequest.getMailCc()).toArray(new String[0]))
                .mailBcc(noNullMailCc(sendRawEmailRequest.getMailBcc()).toArray(new String[0]))
                .mailSubject(sendRawEmailRequest.getSubject())
                .mailContent(sendRawEmailRequest.getContent())
                .build();
        try {
            sesApiMailSender.send(message.getMailTo(), message.getMailCc(), message.getMailBcc(), message.getMailSubject(), message.getMailContent(), null);
            return sendRawEmailRequest.getMessageId();
        } catch (Exception e) {
            LOGGER.error("send raw mail message error", e);
            throw new ServiceException("SendRawMailError", "send raw mail message error.");
        }
    }

    @PostMapping(value = "/sendRawEmailWithAttachment", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Perf(operation = Constants.Perf.PRODUCE_MESSAGE)
    public SendRawEmailResponse sendRawEmailWithAttachment(@RequestPart("mail") TestSendRawEmailRequest sendRawEmailRequest,
                                                           @RequestPart(value = "attachments", required = false) List<MultipartFile> attachments) {
        SendEmailMessage message = SendEmailMessage.builder()
                .messageId(sendRawEmailRequest.getMessageId())
                .mailFrom(sendRawEmailRequest.getMailFrom())
                .mailTo(sendRawEmailRequest.getMailTo().toArray(new String[0]))
                .mailCc(noNullMailCc(sendRawEmailRequest.getMailCc()).toArray(new String[0]))
                .mailBcc(noNullMailCc(sendRawEmailRequest.getMailBcc()).toArray(new String[0]))
                .mailSubject(sendRawEmailRequest.getSubject())
                .mailContent(sendRawEmailRequest.getContent())
                .build();
        try {
            SesEmailSender sesEmailSender = new SesEmailSender();
            return sesEmailSender.sendEmail(sendRawEmailRequest.getSource(), sendRawEmailRequest.getSourceArn(), sendRawEmailRequest.getRegion(),
                    message.getMailTo(), message.getMailCc(),
                    message.getMailBcc(), message.getMailSubject(), message.getMailContent(), attachments);
        } catch (Exception e) {
            LOGGER.error("send raw mail message error", e);
            throw new ServiceException("SendRawMailError", "send raw mail message error.");
        }
    }

    @GetMapping(value = "/sendEmail")
    public Boolean sendEmail() {
        LOGGER.info("pc sendEmail test");
        SendTemplateMailHelper helper = new SendTemplateMailHelper();
        String[] to = {"<EMAIL>"};
        String[] bcc = {"<EMAIL>"};
        return helper.sendTemplateMail(to, bcc, "pc sendEmail test", UUID.randomUUID().toString());
    }

    @GetMapping(value = "/deleteHistoryData")
    public long removeHistoryData(@RequestParam(value = "retentionMonths") Integer retentionMonths,
                                  @RequestParam(value = "deleteLimit", defaultValue = "100000") Integer deleteLimit) {
        if (retentionMonths < 1) {
            throw new ServiceException("InvalidField", "The retentionMonths must greater than one");
        }
        return emailRecordService.deleteHistoryData(retentionMonths, deleteLimit);
    }

    private Set<String> noNullMailCc(Set<String> mailCc) {
        return Optional.ofNullable(mailCc).orElse(new HashSet<>());
    }

    @Autowired
    public void setEmailRecordService(EmailRecordService emailRecordService) {
        this.emailRecordService = emailRecordService;
    }

    @Getter
    @Setter
    public static class TestSendRawEmailRequest {

        private String messageId;

        private String mailFrom;
        private Set<String> mailTo;
        private Set<String> mailCc;
        private Set<String> mailBcc;
        private String subject;
        private String content;
        private String source;
        private String sourceArn;
        private String region;
    }
}
