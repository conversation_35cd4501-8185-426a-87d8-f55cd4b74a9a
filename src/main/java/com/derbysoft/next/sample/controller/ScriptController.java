package com.derbysoft.next.sample.controller;

import com.derbysoft.next.commons.core.exception.ServiceException;
import com.derbysoft.next.sample.domain.entity.ChannelScriptEntity;
import com.derbysoft.next.sample.domain.entity.HotelSystemScriptEntity;
import com.derbysoft.next.sample.service.ScriptRecordService;
import org.apache.commons.io.IOUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Optional;

@RestController
public class ScriptController {

    @Autowired
    private ScriptRecordService scriptRecordService;

    @GetMapping("/channel/{channelId}/script/{scriptTag}/{scriptVersion}/content")
    public ResponseEntity<?> channelContent(@PathVariable String channelId, @PathVariable String scriptTag,
                                            @PathVariable String scriptVersion, HttpServletResponse response) throws IOException {
        ChannelScriptEntity channelScriptEntity = scriptRecordService.loadChannelScript(channelId, scriptTag, scriptVersion);
        return getResponse(response, channelScriptEntity == null, Optional.ofNullable(channelScriptEntity).map(ChannelScriptEntity::getScriptContent).orElse(null));
    }

    @Nullable
    private ResponseEntity<?> getResponse(HttpServletResponse response, boolean isNull, String scriptContent) throws IOException {
        if (isNull) {
            return new ResponseEntity<>(new ServiceException("ScriptNotFound", "script not found."), HttpStatus.INTERNAL_SERVER_ERROR);
        }

        response.setHeader("Content-Type", "text/plain");
        IOUtils.write(scriptContent, response.getOutputStream(), Charset.defaultCharset());
        response.getOutputStream().flush();
        return null;
    }

    @PostMapping("/channel/{channelId}/script/{scriptTag}/{scriptVersion}/content")
    @ResponseBody
    public ResponseEntity<?> saveChannelContent(@PathVariable String channelId, @PathVariable String scriptTag,
                                                @PathVariable String scriptVersion, HttpServletRequest request) throws IOException {
        String scriptContent = IOUtils.toString(request.getInputStream(), Charset.defaultCharset());

        ChannelScriptEntity channelScriptEntity = scriptRecordService.loadChannelScript(channelId, scriptTag, scriptVersion);
        if (channelScriptEntity == null) {
            channelScriptEntity = new ChannelScriptEntity();
            channelScriptEntity.setChannelId(channelId);
            channelScriptEntity.setScriptTag(scriptTag);
            channelScriptEntity.setScriptVersion(scriptVersion);
        }
        channelScriptEntity.setScriptContent(scriptContent);
        scriptRecordService.saveChannelScript(channelScriptEntity);

        return new ResponseEntity<>("Successful", HttpStatus.OK);
    }

    @GetMapping("/hotelSystem/{hotelSystemId}/{apiType}/{hotelSystemVer}/{scriptTag}/{scriptVersion}/content")
    public ResponseEntity<?> hotelSystemContent(@PathVariable String hotelSystemId, @PathVariable String apiType,
                                                @PathVariable String hotelSystemVer, @PathVariable String scriptTag,
                                                @PathVariable String scriptVersion, HttpServletResponse response) throws IOException {
        HotelSystemScriptEntity hotelSystemScriptEntity = scriptRecordService.loadHotelSystemScript(hotelSystemId, apiType, hotelSystemVer, scriptTag, scriptVersion);
        return getResponse(response, hotelSystemScriptEntity == null, Optional.ofNullable(hotelSystemScriptEntity).map(HotelSystemScriptEntity::getScriptContent).orElse(null));
    }

    @PostMapping("/hotelSystem/{hotelSystemId}/{apiType}/{hotelSystemVer}/{scriptTag}/{scriptVersion}/content")
    @ResponseBody
    public ResponseEntity<?> saveHotelSystemContent(@PathVariable String hotelSystemId, @PathVariable String apiType,
                                                    @PathVariable String hotelSystemVer, @PathVariable String scriptTag,
                                                    @PathVariable String scriptVersion, HttpServletRequest request) throws IOException {
        String content = IOUtils.toString(request.getInputStream(), Charset.defaultCharset());
        saveHotelSystemScript(hotelSystemId, apiType, hotelSystemVer, scriptTag, scriptVersion, content, null);
        return new ResponseEntity<>("Successful", HttpStatus.OK);
    }

    @GetMapping("/hotelSystem/{hotelSystemId}/{apiType}/{hotelSystemVer}/{scriptTag}/{scriptVersion}/template")
    public ResponseEntity<?> hotelSystemTemplate(@PathVariable String hotelSystemId, @PathVariable String apiType,
                                                 @PathVariable String hotelSystemVer, @PathVariable String scriptTag,
                                                 @PathVariable String scriptVersion, HttpServletResponse response) throws IOException {
        HotelSystemScriptEntity hotelSystemScriptEntity = scriptRecordService.loadHotelSystemScript(hotelSystemId, apiType, hotelSystemVer, scriptTag, scriptVersion);
        return getResponse(response, hotelSystemScriptEntity == null, Optional.ofNullable(hotelSystemScriptEntity).map(HotelSystemScriptEntity::getRequestTemplate).orElse(null));
    }

    @PostMapping("/hotelSystem/{hotelSystemId}/{apiType}/{hotelSystemVer}/{scriptTag}/{scriptVersion}/template")
    @ResponseBody
    public ResponseEntity<?> saveHotelSystemTemplate(@PathVariable String hotelSystemId, @PathVariable String apiType,
                                                     @PathVariable String hotelSystemVer, @PathVariable String scriptTag,
                                                     @PathVariable String scriptVersion, HttpServletRequest request) throws IOException {
        String template = IOUtils.toString(request.getInputStream(), Charset.defaultCharset());
        saveHotelSystemScript(hotelSystemId, apiType, hotelSystemVer, scriptTag, scriptVersion, null, template);
        return new ResponseEntity<>("Successful", HttpStatus.OK);
    }

    private void saveHotelSystemScript(String hotelSystemId, String apiType, String hotelSystemVer, String scriptTag, String scriptVersion, String content, String template) {

        HotelSystemScriptEntity hotelSystemScriptEntity = scriptRecordService.loadHotelSystemScript(hotelSystemId, apiType, hotelSystemVer, scriptTag, scriptVersion);
        if (hotelSystemScriptEntity == null) {
            hotelSystemScriptEntity = new HotelSystemScriptEntity();
            hotelSystemScriptEntity.setHotelSystemId(hotelSystemId);
            hotelSystemScriptEntity.setApiType(apiType);
            hotelSystemScriptEntity.setHotelSystemVer(hotelSystemVer);
            hotelSystemScriptEntity.setScriptTag(scriptTag);
            hotelSystemScriptEntity.setScriptVersion(scriptVersion);
        }
        hotelSystemScriptEntity.setScriptContent(content);
        hotelSystemScriptEntity.setRequestTemplate(template);
        scriptRecordService.saveHotelSystemScript(hotelSystemScriptEntity);
    }
}
