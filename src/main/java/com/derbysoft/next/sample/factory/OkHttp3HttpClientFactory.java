package com.derbysoft.next.sample.factory;

import com.derbysoft.dtrace.context.TracingUtils;
import com.derbysoft.dtrace.utils.DTraceUtils;
import com.derbysoft.next.commons.core.http.okhttp3.*;
import com.derbysoft.next.commons.dtrace.support.NextOkhttp3TracingInterceptor;
import com.derbysoft.next.commons.dtrace.support.OkHttpClientProperties;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.MediaType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Component
public class OkHttp3HttpClientFactory {

    @Autowired
    private OkHttpClientProperties okHttpClientProperties;

    public static final Map<String, OkHttp3HttpClient> okHttp3HttpClientHashMap = new ConcurrentHashMap<>();

    public OkHttp3HttpClient get(String mediaType) {
        return okHttp3HttpClientHashMap.computeIfAbsent(mediaType, m -> {
            OkHttp3HttpClient r = adapterClient(okHttpClientProperties);
            // Cache is used for thread safety
            r.setMediaType(MediaType.get(mediaType));
            return r;
        });
    }

    public OkHttp3HttpClient adapterClient(OkHttpClientProperties properties) {
        Dispatcher dispatcher = new Dispatcher(TracingUtils.tracedExecutor((new Dispatcher()).executorService()));
        OkHttp3HttpClient http3HttpClient = new OkHttp3HttpClient(
                (builder) -> builder.connectTimeout(properties.getConnectionTimeout(), TimeUnit.MILLISECONDS)
                        .readTimeout(properties.getReadTimeout(), TimeUnit.MILLISECONDS)
                        .writeTimeout(properties.getWriteTimeout(), TimeUnit.MILLISECONDS)
                        .connectionPool(new ConnectionPool(properties.getMaxConnections(), properties.getTimeToLive(), TimeUnit.MILLISECONDS))
                        .dispatcher(dispatcher)
                        .sslSocketFactory(HttpsTrustConfigurer.getSSLSocketFactory(), HttpsTrustConfigurer.getX509TrustManager())
                        .hostnameVerifier(HttpsTrustConfigurer.getHostnameVerifier())
        );
        http3HttpClient.setInterceptors(
                Arrays.asList(
                        new NextOkhttp3TracingInterceptor(DTraceUtils.getTracer()),
                        new ServiceInvocationFailedExceptionInterceptor(),
                        new HttpStreamLogInterceptor()),
                Arrays.asList(new HttpAccessLogInterceptor()));
        return http3HttpClient;
    }
}
