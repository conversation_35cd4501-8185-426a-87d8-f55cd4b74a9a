package com.derbysoft.next.sample;

import com.derbysoft.next.commons.dtrace.util.DTracedExecutorServiceHelper;
import com.derbysoft.next.commons.util.id.IdGenerator;
import com.derbysoft.next.commons.util.id.SnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class NextPropertyConnectMSCConfiguration {

    @Value("${idWorker.nodeId:1}")
    private Integer nodeId;

    @Bean
    public IdGenerator<Long> idWorker() {
        return new SnowflakeIdGenerator(nodeId);
    }

    @Bean(name = "executorService")
    public ExecutorService syncReservationToPMSExecutorService() {
        return DTracedExecutorServiceHelper.tracedExecutor(new ThreadPoolExecutor(
                50,
                50,
                0L,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(10000),
                new CustomizableThreadFactory("executor-thread-")));
    }
}
