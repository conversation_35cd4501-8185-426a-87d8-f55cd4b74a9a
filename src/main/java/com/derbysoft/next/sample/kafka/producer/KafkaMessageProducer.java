package com.derbysoft.next.sample.kafka.producer;

import com.derbysoft.next.commons.core.perf.PerfTracer;
import com.derbysoft.next.sample.util.Constants;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
public class KafkaMessageProducer {

    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaMessageProducer.class);

    private KafkaTemplate<String, byte[]> kafkaTemplate;

    public RecordMetadata send(String topic, Integer partition, String key, byte[] value) {
        return doSend(new ProducerRecord<>(topic, partition, key, value));
    }

    public RecordMetadata send(String topic, String key, byte[] value) {
        return doSend(new ProducerRecord<>(topic, key, value));
    }

    private RecordMetadata doSend(ProducerRecord<String, byte[]> record) {
        try {
            PerfTracer.get().parameter(Constants.PerfField.MESSAGE_TOPIC, record.topic());
            PerfTracer.get().parameter(Constants.PerfField.MESSAGE_KEY, record.key());
            PerfTracer.get().parameter(Constants.PerfField.MESSAGE_SIZE, record.value() != null ? record.value().length : 0);

            var future = kafkaTemplate.send(record);
            SendResult<String, byte[]> sendResult = future.get();
            RecordMetadata recordMetadata = sendResult.getRecordMetadata();

            PerfTracer.get().parameter(Constants.PerfField.OFFSET, recordMetadata.offset());
            PerfTracer.get().parameter(Constants.PerfField.PARTITION, recordMetadata.partition());
            PerfTracer.get().parameter(Constants.PerfField.MESSAGE_TIMESTAMP, recordMetadata.timestamp());
            return recordMetadata;
        } catch (Exception e) {
            LOGGER.error("send message to kafka error, topic=" + record.topic() + ", key=" + record.key(), e);
            throw new IllegalStateException("send message to kafka error, topic=" + record.topic() + ", key=" + record.key());
        }
    }

    @Autowired
    public void setKafkaTemplate(KafkaTemplate<String, byte[]> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }
}
