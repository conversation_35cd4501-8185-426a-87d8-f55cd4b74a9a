package com.derbysoft.next.sample.kafka.processor;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ZipUtil;
import com.derbysoft.next.commons.core.perf.PerfTracer;
import com.derbysoft.next.commons.core.perf.annotation.Perf;
import com.derbysoft.next.commons.util.id.IdGenerator;
import com.derbysoft.next.sample.domain.entity.MessageRecordEntity;
import com.derbysoft.next.sample.kafka.KafkaConfiguration;
import com.derbysoft.next.sample.kafka.message.ChannelMessageHolder;
import com.derbysoft.next.sample.kafka.producer.KafkaMessageProducer;
import com.derbysoft.next.sample.service.ChannelMessageService;
import com.derbysoft.next.sample.util.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 */
@Component
public class ChannelMessageParseProcessor implements MessageProcessor<ChannelMessageHolder> {

    private ChannelMessageService channelMessageService;
    private KafkaMessageProducer kafkaMessageProducer;
    private KafkaConfiguration kafkaConfiguration;
    private ExecutorService executorService;
    private IdGenerator<Long> idWorker;

    @Override
    public String getMessageType() {
        return ChannelMessageHolder.class.getSimpleName();
    }

    @Override
    public void process(ChannelMessageHolder recordHolder) {
        var message = recordHolder.getMessage();
        String channelId = message.key().toUpperCase();
        String msgContent = ZipUtil.unGzip(message.value(), CharsetUtil.UTF_8);
        String derbyMessageId = String.valueOf(idWorker.generate());
        MessageRecordEntity record = new MessageRecordEntity();
        channelMessageService.parseMessage(derbyMessageId, channelId, msgContent, record, null);
        if (Objects.nonNull(record) && MessageRecordEntity.Status.PENDING.equalsIgnoreCase(record.getStatus())) {
            PerfTracer.get().parameter(Constants.PerfField.EXT + "ProducerMessage", String.valueOf(true));
            var messageKey = record.getHotelId() + record.getChannelId();
            var messageId = record.getDerbyMessageId();
            executorService.execute(() -> produceMessage(messageKey, messageId));
        }
    }

    @Perf(operation = Constants.Perf.PRODUCE_MESSAGE)
    private void produceMessage(String messageKey, String messageId) {
        kafkaMessageProducer.send(kafkaConfiguration.getMessageSendTopic(), messageKey, ZipUtil.gzip(messageId, CharsetUtil.UTF_8));
    }

    @Autowired
    public void setChannelMessageService(ChannelMessageService channelMessageService) {
        this.channelMessageService = channelMessageService;
    }

    @Autowired
    public void setKafkaMessageProducer(KafkaMessageProducer kafkaMessageProducer) {
        this.kafkaMessageProducer = kafkaMessageProducer;
    }

    @Autowired
    public void setKafkaConfiguration(KafkaConfiguration kafkaConfiguration) {
        this.kafkaConfiguration = kafkaConfiguration;
    }

    @Autowired
    public void setIdWorker(IdGenerator<Long> idWorker) {
        this.idWorker = idWorker;
    }

    @Autowired
    public void setExecutorService(ExecutorService executorService) {
        this.executorService = executorService;
    }
}
