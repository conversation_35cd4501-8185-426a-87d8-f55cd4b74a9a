package com.derbysoft.next.sample.kafka.message;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.apache.kafka.clients.consumer.ConsumerRecord;

/**
 * <AUTHOR>
 */
@ToString
@EqualsAndHashCode
public abstract class AbstractKafkaMessageHolder<K,V> {

    @Getter
    protected ConsumerRecord<K, V> message;

    AbstractKafkaMessageHolder(ConsumerRecord<K, V> message) {
        this.message = message;
    }

    public String getMessageType() {
        return getClass().getSimpleName();
    }
}
