package com.derbysoft.next.sample.kafka;

import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;

import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
@EnableKafka
@ConfigurationProperties("next.kafka.topics")
@Getter
@Setter
public class KafkaConfiguration {

    private String channelMessageTopic;
    private String messageSendTopic;
    private String rawMailMessageTopic;
    private String templateMailMessageTopic;

    public List<String> mailMessageTopic() {
        return Lists.newArrayList(rawMailMessageTopic, templateMailMessageTopic);
    }
}
