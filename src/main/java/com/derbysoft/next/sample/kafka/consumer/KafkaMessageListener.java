package com.derbysoft.next.sample.kafka.consumer;

import com.derbysoft.next.commons.core.perf.PerfTracer;
import com.derbysoft.next.commons.core.perf.annotation.Perf;
import com.derbysoft.next.sample.kafka.message.AbstractKafkaMessageHolder;
import com.derbysoft.next.sample.kafka.message.ChannelMessageHolder;
import com.derbysoft.next.sample.kafka.message.MailMessageHolder;
import com.derbysoft.next.sample.kafka.message.MessageIdHolder;
import com.derbysoft.next.sample.kafka.processor.MessageProcessor;
import com.derbysoft.next.sample.util.Constants;
import com.google.gson.Gson;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: cy.wang
 */
@Component
public class KafkaMessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaMessageListener.class);

    private Map<String, MessageProcessor> messageProcessorMap;

    private Gson gson;

    @KafkaListener(topics = "#{kafkaConfiguration.channelMessageTopic}", containerFactory = "defaultContainerFactory")
    @Perf(operation = Constants.Perf.CONSUME_MESSAGE)
    public void onChannelMessage(ConsumerRecord<String, byte[]> message, Acknowledgment ack) {
        PerfTracer.get().parameter(Constants.PerfField.MESSAGE_TYPE, "ChannelMessage");
        this.process(message, ack, msg -> new ChannelMessageHolder(msg));
    }

    @KafkaListener(topics = "#{kafkaConfiguration.messageSendTopic}", containerFactory = "defaultContainerFactory")
    @Perf(operation = Constants.Perf.CONSUME_MESSAGE)
    public void onMessageSend(ConsumerRecord<String, byte[]> message, Acknowledgment ack) {
        PerfTracer.get().parameter(Constants.PerfField.MESSAGE_TYPE, "ProcessSendMessage");
        this.process(message, ack, msg -> new MessageIdHolder(msg));
    }

    @KafkaListener(topics = "#{kafkaConfiguration.mailMessageTopic()}", containerFactory = "defaultContainerFactory")
    @Perf(operation = Constants.Perf.CONSUME_MESSAGE)
    public void onMailMessage(ConsumerRecord<String, byte[]> message, Acknowledgment ack) {
        this.process(message, ack, msg -> new MailMessageHolder(msg, gson));
    }

    private <T extends AbstractKafkaMessageHolder> void process(ConsumerRecord<String, byte[]> message, Acknowledgment ack, Function<ConsumerRecord<String, byte[]>, T> function) {
        PerfTracer.get().parameter(Constants.PerfField.MESSAGE_TOPIC, message.topic());
        PerfTracer.get().parameter(Constants.PerfField.MESSAGE_KEY, message.key());

        try {
            PerfTracer.get().parameter(Constants.PerfField.OFFSET, message.offset());
            PerfTracer.get().parameter(Constants.PerfField.PARTITION, message.partition());
            PerfTracer.get().parameter(Constants.PerfField.MESSAGE_TIMESTAMP, message.timestamp());

            T messageHolder = function.apply(message);
            MessageProcessor messageProcessor = messageProcessorMap.get(messageHolder.getMessageType());
            if (messageProcessor == null) {
                throw new IllegalStateException("messageProcessor not found for message: " + messageHolder.getMessageType());
            }
            messageProcessor.process(messageHolder);
        } catch (Exception e) {
            PerfTracer.get().fail(e);
            String recordInfo = String.format("process kafka message error, record info: topic=%s, partition=%s, offset=%s, key=%s",
                    message.topic(),
                    message.partition(),
                    message.offset(),
                    message.key());
            LOGGER.error("process kafka message error, record info:" + recordInfo, e);
        } finally {
            ack.acknowledge();
        }
    }

    @Autowired
    public void setMessageProcessorMap(List<MessageProcessor> messageProcessors) {
        if (CollectionUtils.isEmpty(messageProcessors)) {
            messageProcessorMap = new HashMap<>();
            return;
        }
        this.messageProcessorMap = messageProcessors.stream().collect(Collectors.toMap(MessageProcessor::getMessageType, Function.identity()));
    }

    @Autowired
    public void setGson(Gson gson) {
        this.gson = gson;
    }
}

