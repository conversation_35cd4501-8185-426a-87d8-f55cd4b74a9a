package com.derbysoft.next.sample.kafka.processor;

import com.derbysoft.next.commons.core.perf.PerfTracer;
import com.derbysoft.next.sample.domain.entity.EmailRecordEntity;
import com.derbysoft.next.sample.kafka.message.MailMessageHolder;
import com.derbysoft.next.sample.service.MailServerService;
import com.derbysoft.next.sample.util.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class MailMessageMessageProcessor implements MessageProcessor<MailMessageHolder> {

    private MailServerService mailServerService;

    @Override
    public String getMessageType() {
        return MailMessageHolder.class.getSimpleName();
    }

    @Override
    public void process(MailMessageHolder recordHolder) {
        EmailRecordEntity emailRecord = recordHolder.build(EmailRecordEntity.class);
        emailRecord.setExtraInfo(recordHolder.extractRecordInfo());

        PerfTracer.get().parameter(Constants.PerfField.MESSAGE_ID_DERBY, emailRecord.getMessageGroupId());
        PerfTracer.get().parameter(Constants.PerfField.CHILD_MESSAGE_ID, emailRecord.getMessageId());

        String key = recordHolder.getMessage().key();
        if (key.startsWith(Constants.Kafka.KAFKA_TEMPLATE_MAIL_MESSAGE_KEY)) {
            mailServerService.sendTemplatedEmail(emailRecord);
        } else {
            mailServerService.sendRawEmail(emailRecord);
        }
    }

    @Autowired
    public void setMailServerService(MailServerService mailServerService) {
        this.mailServerService = mailServerService;
    }
}
