package com.derbysoft.next.sample.kafka.processor;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ZipUtil;
import com.derbysoft.next.commons.core.perf.PerfTracer;
import com.derbysoft.next.sample.kafka.message.MessageIdHolder;
import com.derbysoft.next.sample.service.MessageRecordService;
import com.derbysoft.next.sample.util.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class MessageSendProcessor implements MessageProcessor<MessageIdHolder> {

    private MessageRecordService messageRecordService;

    @Override
    public String getMessageType() {
        return MessageIdHolder.class.getSimpleName();
    }

    @Override
    public void process(MessageIdHolder recordHolder) {
        var message = recordHolder.getMessage();
        String derbyMessageId = ZipUtil.unGzip(message.value(), CharsetUtil.UTF_8);
        PerfTracer.get().parameter(Constants.PerfField.MESSAGE_ID_DERBY, derbyMessageId);
        messageRecordService.processMessageRecord(derbyMessageId);
    }

    @Autowired
    public void setMessageRecordService(MessageRecordService messageRecordService) {
        this.messageRecordService = messageRecordService;
    }
}
