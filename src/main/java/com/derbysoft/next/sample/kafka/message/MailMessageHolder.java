package com.derbysoft.next.sample.kafka.message;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ZipUtil;
import com.google.gson.Gson;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class MailMessageHolder extends AbstractKafkaMessageHolder<String, byte[]> {

    private Gson gson;

    public MailMessageHolder(ConsumerRecord<String, byte[]> message, Gson gson) {
        super(message);
        this.gson = gson;
    }

    public <T> T build(Class<T> classOfT) {
        String decompress = ZipUtil.unGzip(message.value(), CharsetUtil.UTF_8);
        return gson.fromJson(decompress, classOfT);
    }

    public Map<String, Object> extractRecordInfo() {
        Map<String, Object> recordInfoMap = new HashMap<>();
        recordInfoMap.put("topic", message.topic());
        recordInfoMap.put("partition", message.partition());
        recordInfoMap.put("offset", message.offset());
        recordInfoMap.put("key", message.key());
        return recordInfoMap;
    }
}
