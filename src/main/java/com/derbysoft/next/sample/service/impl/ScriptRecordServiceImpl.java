package com.derbysoft.next.sample.service.impl;

import com.derbysoft.next.sample.domain.entity.ChannelScriptEntity;
import com.derbysoft.next.sample.domain.entity.HotelSystemScriptEntity;
import com.derbysoft.next.sample.domain.repository.ChannelScriptRepository;
import com.derbysoft.next.sample.domain.repository.HotelSystemScriptRepository;
import com.derbysoft.next.sample.service.ScriptRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class ScriptRecordServiceImpl implements ScriptRecordService {

    @Autowired
    private ChannelScriptRepository channelScriptRepository;
    @Autowired
    private HotelSystemScriptRepository hotelSystemScriptRepository;

    @Override
    public ChannelScriptEntity loadChannelScript(String channelId, String scriptTag, String scriptVer) {
        return channelScriptRepository.findByChannelIdAndScriptTagAndScriptVersion(channelId, scriptTag, scriptVer);
    }

    @Override
    public ChannelScriptEntity saveChannelScript(ChannelScriptEntity channelScript) {
        return channelScriptRepository.save(channelScript);
    }

    @Override
    public HotelSystemScriptEntity loadHotelSystemScript(String hotelSystemId, String apiType, String hotelSystemVer, String scriptTag, String scriptVersion) {
        return hotelSystemScriptRepository.findByHotelSystemIdAndApiTypeAndHotelSystemVerAndScriptTagAndScriptVersion(hotelSystemId, apiType, hotelSystemVer, scriptTag, scriptVersion);
    }

    @Override
    public HotelSystemScriptEntity saveHotelSystemScript(HotelSystemScriptEntity hotelSystemScript) {
        return hotelSystemScriptRepository.save(hotelSystemScript);
    }
}
