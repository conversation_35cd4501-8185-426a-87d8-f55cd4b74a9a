package com.derbysoft.next.sample.service.impl;

import com.derbysoft.next.commons.core.exception.ServiceException;
import com.derbysoft.next.commons.core.perf.PerfTracer;
import com.derbysoft.next.commons.util.compress.CompressUtil;
import com.derbysoft.next.sample.client.PcProfileFeignClient;
import com.derbysoft.next.sample.domain.entity.ChannelScriptEntity;
import com.derbysoft.next.sample.domain.entity.MessageRecordEntity;
import com.derbysoft.next.sample.dto.profile.ChannelDTO;
import com.derbysoft.next.sample.factory.OkHttp3HttpClientFactory;
import com.derbysoft.next.sample.service.ChannelMessageService;
import com.derbysoft.next.sample.service.MessageRecordService;
import com.derbysoft.next.sample.service.ScriptRecordService;
import com.derbysoft.next.sample.service.ScriptService;
import com.derbysoft.next.sample.util.CommonUtils;
import com.derbysoft.next.sample.util.Constants;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Service
@Transactional
public class ChannelMessageServiceImpl implements ChannelMessageService {

    @Autowired
    private MessageRecordService messageRecordService;
    @Autowired
    @Qualifier("scriptService")
    private ScriptService scriptService;
    @Autowired
    private PcProfileFeignClient profileClient;
    @Autowired
    private Gson gson;
    @Autowired
    private OkHttp3HttpClientFactory clientFactory;
    @Autowired
    private ScriptRecordService scriptRecordService;

    @Override
    public void parseMessage(String derbyMessageId, String channelId, String msgContent, MessageRecordEntity msgRecord, Boolean forceUpd) {
        PerfTracer.get().parameter(Constants.PerfField.MESSAGE_ID_DERBY, derbyMessageId);

        msgRecord.setChannelId(channelId);
        msgRecord.setDerbyMessageId(derbyMessageId);
        msgRecord.setOriginMessage(CompressUtil.compressStr(msgContent));
        try {
            ChannelDTO channel = profileClient.findChannel(channelId);
            String scriptVer = Optional.ofNullable(channel).map(e ->
                    Optional.ofNullable(e.getSettings())
                            .map(settings -> CommonUtils.toString(settings.getOrDefault(Constants.ScriptVersion.MSG_PARSE_VER, Constants.ScriptVersion.DEFAULT_VERSION)))
                            .orElse(Constants.ScriptVersion.DEFAULT_VERSION))
                    .orElse(Constants.ScriptVersion.DEFAULT_VERSION);
            ChannelScriptEntity script = scriptRecordService.loadChannelScript(channelId, Constants.ScriptTag.PARSE, scriptVer);
            String scriptContent = Optional.ofNullable(script).map(ChannelScriptEntity::getScriptContent).orElse(null);
            if (StringUtils.isEmpty(scriptContent)) {
                throw new ServiceException("ScriptNotConfig", MessageFormat.format("channel:{0} script not config", channelId));
            }
            scriptService.execute(scriptContent, this.buildProperties(channelId, msgContent, msgRecord));
            PerfTracer.get().parameter(Constants.PerfField.EXT + Constants.PerfField.MESSAGE_ID_CHANNEL,
                    String.join("_", msgRecord.getChannelId(), msgRecord.getThreadId(), msgRecord.getMessageId()));
        } catch (ServiceException e) {
            msgRecord.setStatus(MessageRecordEntity.Status.FAILED);
            msgRecord.setErrorMessage(e.getErrorMessage());
            PerfTracer.get().fail(e.getErrorMessage());
        } catch (Exception e) {
            msgRecord.setStatus(MessageRecordEntity.Status.FAILED);
            msgRecord.setErrorMessage(ExceptionUtils.getStackTrace(e));
            PerfTracer.get().fail(e.getMessage());
        }
        var entity = messageRecordService.findMessageRecord(msgRecord.getChannelId(), msgRecord.getThreadId(), msgRecord.getMessageId());
        if (Objects.nonNull(entity) && Optional.ofNullable(forceUpd).orElse(false)) {
            messageRecordService.deleteMessageRecord(entity);
        } else if (Objects.nonNull(entity)) {
            return;
        }
        messageRecordService.addMessageRecord(msgRecord);
    }

    private Map<String, Object> buildProperties(String key, String msgContent, MessageRecordEntity msgRecord) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("channelId", key);
        properties.put("msgContent", msgContent);
        properties.put("profileClient", profileClient);
        properties.put("record", msgRecord);
        properties.put("gson", gson);
        properties.put("clientFactory", clientFactory);

        Map scriptProperties = new HashMap();
        scriptProperties.put("properties", properties);
        return scriptProperties;
    }
}
