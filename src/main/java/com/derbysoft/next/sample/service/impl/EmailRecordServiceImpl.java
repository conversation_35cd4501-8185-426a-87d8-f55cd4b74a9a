package com.derbysoft.next.sample.service.impl;

import com.derbysoft.next.commons.core.exception.ServiceException;
import com.derbysoft.next.sample.domain.entity.EmailRecordEntity;
import com.derbysoft.next.sample.domain.repository.EmailRecordRepository;
import com.derbysoft.next.sample.dto.SearchEmailRecordsRequest;
import com.derbysoft.next.sample.dto.SearchEmailRecordsResponse;
import com.derbysoft.next.sample.dto.enums.MailStatus;
import com.derbysoft.next.sample.service.EmailRecordService;
import com.mongodb.client.result.DeleteResult;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class EmailRecordServiceImpl implements EmailRecordService {

    @Autowired
    private EmailRecordRepository emailRecordRepository;

    private MongoTemplate mongoTemplate;

    @Override
    public EmailRecordEntity addEmailRecord(EmailRecordEntity entity) {
        return emailRecordRepository.save(entity);
    }

    @Override
    public EmailRecordEntity emailSentSuccessfully(String messageType, String messageId, Map<String, Object> extraInfo, String supplier) {
        EmailRecordEntity emailRecord = emailRecordRepository.findByMessageIdAndMessageType(messageId, messageType);
        if (emailRecord == null) {
            throw new ServiceException("EmailRecordNotFound", "EmailRecord not found.");
        }
        emailRecord.setStatus(MailStatus.Sends.toString());
        emailRecord.setSupplier(supplier);
        emailRecord.setExtraInfo(extraInfo);
        emailRecord.setSendTime(DateTime.now());
        return updateEmailRecord(emailRecord);
    }

    @Override
    public EmailRecordEntity emailSentFailure(String messageType, String messageId, Map<String, Object> extraInfo) {
        EmailRecordEntity emailRecord = emailRecordRepository.findByMessageIdAndMessageType(messageId, messageType);
        if (emailRecord == null) {
            throw new ServiceException("EmailRecordNotFound", "EmailRecord not found.");
        }
        emailRecord.setStatus(MailStatus.Failure.toString());
        emailRecord.setExtraInfo(extraInfo);
        return updateEmailRecord(emailRecord);
    }

    @Override
    public EmailRecordEntity emailRenderingFailure(String messageType, String messageId) {
        EmailRecordEntity emailRecord = emailRecordRepository.findByMessageIdAndMessageType(messageId, messageType);
        if (emailRecord == null) {
            throw new ServiceException("EmailRecordNotFound", "EmailRecord not found.");
        }
        emailRecord.setStatus(MailStatus.RenderingFailure.toString());
        return updateEmailRecord(emailRecord);
    }

    @Override
    public EmailRecordEntity updateEmailRecord(EmailRecordEntity entity) {
        return emailRecordRepository.save(entity);
    }

    @Override
    public SearchEmailRecordsResponse searchEmailRecords(SearchEmailRecordsRequest request) {
        // 20240524 modify by andrew.yang 6593159538 start
        Page<EmailRecordEntity> page = this.search(request, "messageGroupId");
        if (page.isEmpty()) {
            page = this.search(request, "messageId");
        }
        // 20240524 modify by andrew.yang 6593159538 end

        SearchEmailRecordsResponse response = new SearchEmailRecordsResponse();
        response.setContent(page.getContent());
        response.setTotal(page.getTotalElements());
        return response;
    }

    // 20240524 modify by andrew.yang 6593159538 start
    private Page<EmailRecordEntity> search(SearchEmailRecordsRequest request, String messageIdType) {
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withMatcher("messageType", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("mailFrom", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("mailTo", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("mailCc", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("supplier", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("status", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("template", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("subject", ExampleMatcher.GenericPropertyMatchers.contains())
                .withMatcher("content", ExampleMatcher.GenericPropertyMatchers.contains())
                .withMatcher("templateData", ExampleMatcher.GenericPropertyMatchers.contains());
        if ("messageGroupId".equalsIgnoreCase(messageIdType)) {
            exampleMatcher.withMatcher("messageGroupId", ExampleMatcher.GenericPropertyMatchers.exact());
        } else {
            exampleMatcher.withMatcher("messageId", ExampleMatcher.GenericPropertyMatchers.exact());
        }

        EmailRecordEntity entity = EmailRecordEntity.builder().build();
        if (StringUtils.isNotBlank(request.getMessageId())) {
            if ("messageGroupId".equalsIgnoreCase(messageIdType)) {
                entity.setMessageGroupId(request.getMessageId());
            } else {
                entity.setMessageId(request.getMessageId());
            }
        }
        if (StringUtils.isNotBlank(request.getMessageType())) {
            entity.setMessageType(request.getMessageType());
        }
        if (StringUtils.isNotBlank(request.getMailFrom())) {
            entity.setMailFrom(request.getMailFrom());
        }
        if (!CollectionUtils.isEmpty(request.getMailTo())) {
            entity.setMailTo(request.getMailTo());
        }
        if (!CollectionUtils.isEmpty(request.getMailCc())) {
            entity.setMailCc(request.getMailCc());
        }
        if (StringUtils.isNotBlank(request.getSubject())) {
            entity.setSubject(request.getSubject());
        }
        if (StringUtils.isNotBlank(request.getStatus())) {
            entity.setStatus(request.getStatus());
        }
        if (StringUtils.isNotBlank(request.getSupplier())) {
            entity.setSupplier(request.getSupplier());
        }
        if (StringUtils.isNotBlank(request.getContent())) {
            entity.setContent(request.getContent());
        }
        if (StringUtils.isNotBlank(request.getTemplate())) {
            entity.setTemplate(request.getTemplate());
        }
        if (!CollectionUtils.isEmpty(request.getTemplateData())) {
            entity.setTemplateData(request.getTemplateData());
        }
        Example<EmailRecordEntity> example = Example.of(entity, exampleMatcher);
        PageRequest pageRequest = PageRequest.of(request.getPage(), request.getSize(), Sort.by(Sort.Direction.DESC, "createdDate"));
        return emailRecordRepository.findAll(example, pageRequest);
    }
    // 20240524 modify by andrew.yang 6593159538 end

    @Override
    public long deleteHistoryData(Integer retentionMonths, Integer deleteLimit) {
        DateTime now = DateTime.now();
        DateTime beforeDateTime = now.minusMonths(retentionMonths);

        Query query = new Query();
        query.addCriteria(Criteria.where("createdDate").lt(beforeDateTime));
        query.limit(deleteLimit);
        DeleteResult deleteResult = mongoTemplate.remove(query, EmailRecordEntity.class);
        return deleteResult.getDeletedCount();
    }

    @Autowired
    public void setMongoTemplate(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }
}
