package com.derbysoft.next.sample.service;

import com.derbysoft.next.sample.domain.entity.MessageRecordEntity;

/**
 * <AUTHOR>
 */
public interface MessageRecordService {

    void deleteMessageRecord(MessageRecordEntity record);

    MessageRecordEntity addMessageRecord(MessageRecordEntity entity);

    MessageRecordEntity findMessageRecord(String channel, String threadId, String messageId);

    MessageRecordEntity findMessageRecordByDerbyMessageId(String derbyMessageId);

    void processMessageRecord(String derbyMessageId);

    void parseAndProcessMessage(String derbyMessageId);
}
