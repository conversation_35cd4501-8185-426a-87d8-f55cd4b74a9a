package com.derbysoft.next.sample.service;

import com.derbysoft.next.sample.domain.entity.EmailRecordEntity;
import com.derbysoft.next.sample.dto.SearchEmailRecordsRequest;
import com.derbysoft.next.sample.dto.SearchEmailRecordsResponse;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface EmailRecordService {

    EmailRecordEntity addEmailRecord(EmailRecordEntity entity);

    EmailRecordEntity emailSentSuccessfully(String messageType, String messageId, Map<String, Object> extraInfo, String supplier);

    EmailRecordEntity emailSentFailure(String messageType, String messageId, Map<String, Object> extraInfo);

    EmailRecordEntity emailRenderingFailure(String messageType,String messageId);

    EmailRecordEntity updateEmailRecord(EmailRecordEntity entity);

    SearchEmailRecordsResponse searchEmailRecords(SearchEmailRecordsRequest request);

    long deleteHistoryData(Integer retentionMonths, Integer deleteLimit);
}
