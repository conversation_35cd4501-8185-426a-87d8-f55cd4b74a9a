package com.derbysoft.next.sample.service.impl;

import com.derbysoft.next.commons.core.exception.ServiceException;
import com.derbysoft.next.commons.core.perf.PerfTracer;
import com.derbysoft.next.sample.client.PcCMFeignClient;
import com.derbysoft.next.sample.client.PcProfileFeignClient;
import com.derbysoft.next.sample.domain.entity.HotelSystemScriptEntity;
import com.derbysoft.next.sample.domain.entity.MessageRecordEntity;
import com.derbysoft.next.sample.factory.OkHttp3HttpClientFactory;
import com.derbysoft.next.sample.service.HotelSystemMessageService;
import com.derbysoft.next.sample.service.ScriptRecordService;
import com.derbysoft.next.sample.service.ScriptService;
import com.derbysoft.next.sample.util.Constants;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
@Transactional
public class HotelSystemMessageServiceImpl implements HotelSystemMessageService {

    @Autowired
    private PcProfileFeignClient profileClient;
    @Autowired
    private PcCMFeignClient cmClient;
    @Autowired
    @Qualifier("scriptService")
    private ScriptService scriptService;
    @Autowired
    private Gson gson;
    @Autowired
    private OkHttp3HttpClientFactory clientFactory;
    @Autowired
    private ScriptRecordService scriptRecordService;

    @Override
    public void sendMessage(MessageRecordEntity record) {
        try {
            record.setStatus(MessageRecordEntity.Status.PROCESSING);
            HotelSystemScriptEntity script = scriptRecordService.loadHotelSystemScript(record.getHotelSystemId(), record.getApiType(),
                    record.getHotelSystemVer(), Constants.ScriptTag.SEND, record.getScriptVer());
            String scriptContent = Optional.ofNullable(script).map(HotelSystemScriptEntity::getScriptContent).orElse(null);
            if (StringUtils.isEmpty(scriptContent)) {
                throw new ServiceException("ScriptNotConfig", MessageFormat.format("hotel system script not config. derbyMessageId {0}", record.getDerbyMessageId()));
            }
            scriptService.execute(scriptContent, this.buildProperties(record, script.getRequestTemplate()));
        } catch (ServiceException e) {
            record.setStatus(MessageRecordEntity.Status.FAILED);
            record.setErrorMessage(e.getErrorMessage());
            PerfTracer.get().fail(e.getErrorMessage());
        } catch (Exception e) {
            record.setStatus(MessageRecordEntity.Status.FAILED);
            record.setErrorMessage(ExceptionUtils.getStackTrace(e));
            PerfTracer.get().fail(e.getMessage());
        }
    }

    private Map<String, Object> buildProperties(MessageRecordEntity msgRecord, String template) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("profileClient", profileClient);
        properties.put("cmClient", cmClient);
        properties.put("record", msgRecord);
        properties.put("template", template);
        properties.put("gson", gson);
        properties.put("clientFactory", clientFactory);

        Map scriptProperties = new HashMap();
        scriptProperties.put("properties", properties);
        return scriptProperties;
    }
}
