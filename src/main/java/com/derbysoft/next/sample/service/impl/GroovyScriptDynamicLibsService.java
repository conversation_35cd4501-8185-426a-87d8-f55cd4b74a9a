package com.derbysoft.next.sample.service.impl;

import com.derbysoft.next.commons.core.json.gson.converter.BigDecimalConverter;
import com.derbysoft.next.commons.core.json.gson.support.GsonUtils;
import com.google.gson.Gson;
import groovy.lang.GroovyClassLoader;
import org.codehaus.groovy.jsr223.GroovyScriptEngineImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.script.CompiledScript;
import javax.script.ScriptException;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringReader;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLStreamHandler;
import java.util.HashMap;
import java.util.Map;

/**
 * GroovyScriptServiceDynamicLibsImpl
 *
 * <AUTHOR>
 */
@Service("scriptService")
public class GroovyScriptDynamicLibsService extends GroovyScriptCacheService {

    public static final String URL_PROTOCOL_GRAB = "grab";
    public static final String UTF_8 = "UTF-8";

    public static final String FIELD_SCRIPT = "script";
    public static final String FIELD_LIBS = "libs";

    private boolean dynamicLibsEnable = false;

    public static final Gson gson = GsonUtils.create(gsonBuilder ->
            gsonBuilder.enableComplexMapKeySerialization()
                    .registerTypeAdapter(BigDecimal.class, new BigDecimalConverter())
    );

    @Override
    protected CompiledScript compile(String script) throws ScriptException, MalformedURLException {
        if (!dynamicLibsEnable) {
            return super.compile(script);
        }
        script = script.trim();
        if (!isJson(script)) {
            return super.compile(script);
        }
        final Map<String, Object> json = (Map<String, Object>) gson.fromJson(new StringReader(script), Map.class);
        final Map<String, String> scriptClasses = getScriptClasses(json);
        final String mainScript = getMainScript(json);

        GroovyClassLoader groovyClassLoader = new GroovyClassLoader();
        groovyClassLoader.addURL(new URL(null, URL_PROTOCOL_GRAB + ":/", createURLStreamHandler(scriptClasses)));
        return new GroovyScriptEngineImpl(groovyClassLoader).compile(mainScript);
    }

    private String getMainScript(Map<String, Object> json) {
        return (String) json.get(FIELD_SCRIPT);
    }

    private Map<String, String> getScriptClasses(Map<String, Object> json) {
        Map<String, String> scriptClasses = new HashMap<>();
        Map<String, String> libs = (Map<String, String>) json.getOrDefault(FIELD_LIBS, new HashMap<>());
        libs.forEach((k, v) -> {
            scriptClasses.put(URL_PROTOCOL_GRAB + ":/" + k, v);
        });
        return scriptClasses;
    }

    private static boolean isJson(String script) {
        return script.startsWith("{") && script.endsWith("}");
    }

    private static URLStreamHandler createURLStreamHandler(Map<String, String> scriptClasses) {
        return new URLStreamHandler() {
            @Override
            protected URLConnection openConnection(URL url) throws IOException {
                return new GrabGSURLConnection(url, scriptClasses);
            }
        };
    }

    private static class GrabGSURLConnection extends URLConnection {

        private Map<String, String> scriptClasses;

        protected GrabGSURLConnection(URL url, Map<String, String> scriptClasses) {
            super(url);
            this.scriptClasses = scriptClasses;
        }

        @Override
        public InputStream getInputStream() throws IOException {
            final String source = scriptClasses.get(this.getURL().toString());
            if (source != null) {
                return new ByteArrayInputStream(source.getBytes(UTF_8));
            }
            throw new IOException("url: " + getURL() + "not found");
        }

        @Override
        public void connect() throws IOException {
        }

    }

    @Value("${dynamic.libs.enable:false}")
    public void setDynamicLibsEnable(boolean dynamicLibsEnable) {
        this.dynamicLibsEnable = dynamicLibsEnable;
    }

}
