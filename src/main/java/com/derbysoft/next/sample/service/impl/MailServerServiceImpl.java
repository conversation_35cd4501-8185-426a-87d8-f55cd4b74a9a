package com.derbysoft.next.sample.service.impl;

import com.derbysoft.next.commons.core.perf.PerfTracer;
import com.derbysoft.next.sample.domain.entity.EmailRecordEntity;
import com.derbysoft.next.sample.dto.SendEmailMessage;
import com.derbysoft.next.sample.dto.SendRawEmailRequest;
import com.derbysoft.next.sample.dto.enums.Language;
import com.derbysoft.next.sample.dto.enums.MessageType;
import com.derbysoft.next.sample.mail.MailSenderBroker;
import com.derbysoft.next.sample.mail.MailTemplateBuilder;
import com.derbysoft.next.sample.service.EmailRecordService;
import com.derbysoft.next.sample.service.MailServerService;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class MailServerServiceImpl implements MailServerService {

    private static final Logger logger = LoggerFactory.getLogger(MailServerService.class);

    private static final String DEFAULT_MAIL_FROM = "pc.noreply";

    private MailSenderBroker mailSenderBroker;

    private MailTemplateBuilder mailTemplateBuilder;

    private EmailRecordService emailRecordService;

    @Override
    public String sendRawEmail(EmailRecordEntity entity) {
        SendEmailMessage message = SendEmailMessage.builder()
                .messageId(entity.getMessageId())
                .mailFrom(noNullMailFrom(entity.getMailFrom()))
                .mailTo(entity.getMailTo().toArray(new String[0]))
                .mailCc(noNullMailCc(entity.getMailCc()).toArray(new String[0]))
                .mailBcc(noNullMailCc(entity.getMailBcc()).toArray(new String[0]))
                .mailSubject(entity.getSubject())
                .mailContent(entity.getContent())
                .build();
        String senderName;
        try {
            senderName = mailSenderBroker.sendMail(message);
            emailRecordService.emailSentSuccessfully(MessageType.RAW.name(), entity.getMessageId(), entity.getExtraInfo(), senderName);
        } catch (Exception e) {
            senderName = null;
            String errorMsg = "Send raw email error: ";
            logger.error(errorMsg, e);
            PerfTracer.get().fail(errorMsg + e.getMessage());
            emailRecordService.emailSentFailure(MessageType.RAW.name(), entity.getMessageId(), entity.getExtraInfo());
        }
        return senderName;
    }

    @Override
    public String sendTemplatedEmail(EmailRecordEntity entity) {
        String template = entity.getTemplate();
        Map<String, Object> variables = Optional.ofNullable(entity.getTemplateData()).orElse(Maps.newHashMap());
        String mailContent;
        try {
            mailContent = mailTemplateBuilder.build(template, variables, Language.EN);
        } catch (Exception e) {
            String errorMsg = "Send templated email fill template error: ";
            logger.error(errorMsg, e);
            PerfTracer.get().fail(errorMsg + e.getMessage());
            emailRecordService.emailRenderingFailure(MessageType.TEMPLATE.name(), entity.getMessageId());
            return null;
        }
        SendEmailMessage message = SendEmailMessage.builder()
                .messageId(entity.getMessageId())
                .mailFrom(noNullMailFrom(entity.getMailFrom()))
                .mailTo(entity.getMailTo().toArray(new String[0]))
                .mailCc(noNullMailCc(entity.getMailCc()).toArray(new String[0]))
                .mailBcc(noNullMailCc(entity.getMailBcc()).toArray(new String[0]))
                .mailSubject(entity.getSubject())
                .mailContent(mailContent)
                .build();
        String senderName;
        try {
            senderName = mailSenderBroker.sendMail(message);
            emailRecordService.emailSentSuccessfully(MessageType.TEMPLATE.name(), entity.getMessageId(), entity.getExtraInfo(), senderName);
        } catch (Exception e) {
            senderName = null;
            String errorMsg = "Send templated email error: ";
            logger.error(errorMsg, e);
            PerfTracer.get().fail(errorMsg + e.getMessage());
            emailRecordService.emailSentFailure(MessageType.TEMPLATE.name(), entity.getMessageId(), entity.getExtraInfo());
        }
        return senderName;
    }

    @Override
    public String sendRawEmailWithAttachment(SendRawEmailRequest request, List<MultipartFile> attachments) {
        SendEmailMessage message = SendEmailMessage.builder()
                .messageId(request.getMessageId())
                .mailFrom(noNullMailFrom(request.getMailFrom()))
                .mailTo(request.getMailTo().toArray(new String[0]))
                .mailCc(noNullMailCc(request.getMailCc()).toArray(new String[0]))
                .mailBcc(noNullMailCc(request.getMailBcc()).toArray(new String[0]))
                .mailSubject(request.getSubject())
                .mailContent(request.getContent())
                .attachments(attachments)
                .build();
        String senderName;
        try {
            senderName = mailSenderBroker.sendMail(message);
        } catch (Exception e) {
            senderName = null;
            String errorMsg = "Send raw email with attachment error: ";
            logger.error(errorMsg, e);
            PerfTracer.get().fail(errorMsg + e.getMessage());
        }
        return senderName;
    }

    private Set<String> noNullMailCc(Set<String> mailCc) {
        return Optional.ofNullable(mailCc).orElse(new HashSet<>());
    }

    private String noNullMailFrom(String mailFrom) {
        return Optional.ofNullable(mailFrom).orElse(MailServerServiceImpl.DEFAULT_MAIL_FROM);
    }

    @Autowired
    public void setMailSenderBroker(MailSenderBroker mailSenderBroker) {
        this.mailSenderBroker = mailSenderBroker;
    }

    @Autowired
    public void setMailTemplateBuilder(MailTemplateBuilder mailTemplateBuilder) {
        this.mailTemplateBuilder = mailTemplateBuilder;
    }

    @Autowired
    public void setEmailRecordService(EmailRecordService emailRecordService) {
        this.emailRecordService = emailRecordService;
    }

}
