package com.derbysoft.next.sample.service.impl;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.derbysoft.next.commons.core.exception.ServiceException;
import com.derbysoft.next.commons.core.http.TransactionType;
import com.derbysoft.next.commons.core.http.UrlWrapper;
import com.derbysoft.next.commons.core.http.okhttp3.OkHttp3HttpClient;
import com.derbysoft.next.sample.domain.entity.EmailRecordEntity;
import com.derbysoft.next.sample.domain.repository.EmailRecordRepository;
import com.derbysoft.next.sample.dto.EmailRetryRequest;
import com.derbysoft.next.sample.dto.KafkaConsumeResponse;
import com.derbysoft.next.sample.dto.enums.MailStatus;
import com.derbysoft.next.sample.dto.enums.MessageType;
import com.derbysoft.next.sample.kafka.KafkaConfiguration;
import com.derbysoft.next.sample.kafka.producer.KafkaMessageProducer;
import com.derbysoft.next.sample.service.EmailRetryService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class EmailRetryServiceImpl implements EmailRetryService {

    private final OkHttp3HttpClient http3HttpClient;

    private final String urlFormat;

    {
        http3HttpClient = new OkHttp3HttpClient();
        urlFormat = "{}/pckafkamanagement/topic/{}/consume?clusterId={}&offset={}&count=1&partition={}&unGzip=true";
    }

    private EmailRecordRepository emailRecordRepository;

    private Gson gson;

    private KafkaConfiguration kafkaConfiguration;
    private KafkaMessageProducer kafkaMessageProducer;

    @Override
    public void emailRetry(EmailRetryRequest emailRetryRequest) {
        EmailRecordEntity emailRecordEntity = emailRecordRepository.findByMessageIdAndMessageType(emailRetryRequest.getMessageId(), emailRetryRequest.getMessageType());
        if (emailRecordEntity == null) {
            throw new ServiceException("EmailRecordNotFound", "Email record not found");
        }
        if (MailStatus.Sends.toString().equals(emailRecordEntity.getStatus())) {
            throw new ServiceException("RetryFailed", "The message could not be retried.");
        }

        Map<String, Object> extraInfo = emailRecordEntity.getExtraInfo();
        String topic = (String) extraInfo.get("topic");
        Integer partition = (Integer) extraInfo.get("partition");
        Long offset = (Long) extraInfo.get("offset");
        String key = (String) extraInfo.get("key");

        String url = StrUtil.format(urlFormat, emailRetryRequest.getKafkaMgmDomain(), topic,
                emailRetryRequest.getKafkaMgmClusterId(), offset, partition);

        UrlWrapper urlWrapper = new UrlWrapper(TransactionType.Legacy.name(), "kafkaConsume", url);

        String response = http3HttpClient.get(urlWrapper, String.class);
        List<KafkaConsumeResponse> kafkaConsumeResponses = gson.fromJson(response, new TypeToken<List<KafkaConsumeResponse>>() {
        }.getType());
        if (kafkaConsumeResponses.isEmpty()) {
            throw new ServiceException("RetryFailed", "kafka consume response is empty.");
        }
        KafkaConsumeResponse kafkaConsumeResponse = kafkaConsumeResponses.get(0);

        String actualTopic;
        if (MessageType.TEMPLATE.name().equals(emailRecordEntity.getTemplate())) {
            actualTopic = kafkaConfiguration.getTemplateMailMessageTopic();
        } else {
            actualTopic = kafkaConfiguration.getRawMailMessageTopic();
        }

        kafkaMessageProducer.send(actualTopic, key, ZipUtil.gzip(kafkaConsumeResponse.getValue(), CharsetUtil.UTF_8));
    }

    @Autowired
    public void setKafkaConfiguration(KafkaConfiguration kafkaConfiguration) {
        this.kafkaConfiguration = kafkaConfiguration;
    }

    @Autowired
    public void setEmailRecordRepository(EmailRecordRepository emailRecordRepository) {
        this.emailRecordRepository = emailRecordRepository;
    }

    @Autowired
    public void setGson(Gson gson) {
        this.gson = gson;
    }

    @Autowired
    public void setKafkaMessageProducer(KafkaMessageProducer kafkaMessageProducer) {
        this.kafkaMessageProducer = kafkaMessageProducer;
    }
}
