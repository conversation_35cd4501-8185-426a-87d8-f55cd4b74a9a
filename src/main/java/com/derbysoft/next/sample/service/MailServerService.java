package com.derbysoft.next.sample.service;

import com.derbysoft.next.sample.domain.entity.EmailRecordEntity;
import com.derbysoft.next.sample.dto.SendRawEmailRequest;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface MailServerService {

    String sendRawEmail(EmailRecordEntity entity);

    String sendTemplatedEmail(EmailRecordEntity entity);

    String sendRawEmailWithAttachment(SendRawEmailRequest request, List<MultipartFile> attachments);
}
