package com.derbysoft.next.sample.service.impl;

import com.derbysoft.next.sample.service.GroovyScriptService;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.script.CompiledScript;
import javax.script.ScriptException;
import javax.script.SimpleBindings;
import java.net.MalformedURLException;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * GroovyScriptCacheService
 *
 * <AUTHOR> Yang
 */
public class GroovyScriptCacheService extends GroovyScriptService {
    public static final Logger logger = LoggerFactory.getLogger(GroovyScriptCacheService.class);

    private LoadingCache<String, CompiledScript> cache;

    public GroovyScriptCacheService() {
        cache = CacheBuilder.newBuilder()
                .maximumSize(1024)
                .expireAfterWrite(1, TimeUnit.DAYS)
                .build(new CacheLoader<String, CompiledScript>() {
                    public CompiledScript load(String script) throws ScriptException, MalformedURLException {
                        return compile(script);
                    }
                });
    }

    public Object execute(String script, Map<String, Object> properties) throws ScriptException, MalformedURLException {
        CompiledScript compiledScript;
        try {
            compiledScript = cache.get(script);
        } catch (ExecutionException e) {
            logger.error("load compile script from cache failed", e.getCause());
            compiledScript = compile(script);
        }

        SimpleBindings bindings = new SimpleBindings();
        bindings.putAll(properties);
        return compiledScript.eval(bindings);
    }
}
