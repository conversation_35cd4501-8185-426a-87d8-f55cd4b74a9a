package com.derbysoft.next.sample.service.impl;

import com.derbysoft.next.commons.core.exception.ServiceException;
import com.derbysoft.next.commons.util.compress.CompressUtil;
import com.derbysoft.next.sample.domain.entity.MessageRecordEntity;
import com.derbysoft.next.sample.domain.repository.MessageRecordRepository;
import com.derbysoft.next.sample.service.ChannelMessageService;
import com.derbysoft.next.sample.service.HotelSystemMessageService;
import com.derbysoft.next.sample.service.MessageRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.Objects;

@Service
@Transactional
@Slf4j
public class MessageRecordServiceImpl implements MessageRecordService {

    @Autowired
    private MessageRecordRepository messageRecordRepository;
    @Autowired
    private HotelSystemMessageService hotelSystemMessageService;
    @Autowired
    @Lazy
    private ChannelMessageService channelMessageService;

    @Override
    public void deleteMessageRecord(MessageRecordEntity record) {
        messageRecordRepository.delete(record);
    }

    @Override
    public MessageRecordEntity addMessageRecord(MessageRecordEntity record) {
        return messageRecordRepository.save(record);
    }

    @Override
    public MessageRecordEntity findMessageRecord(String channel, String threadId, String messageId) {
        return messageRecordRepository.findByChannelIdAndThreadIdAndMessageId(channel, threadId, messageId);
    }

    @Override
    public MessageRecordEntity findMessageRecordByDerbyMessageId(String derbyMessageId) {
        return messageRecordRepository.findByDerbyMessageId(derbyMessageId);
    }

    @Override
    public void processMessageRecord(String derbyMessageId) {
        MessageRecordEntity record = messageRecordRepository.findByDerbyMessageId(derbyMessageId);
        if (Objects.isNull(record)) {
            throw new ServiceException("MessageNotFound", MessageFormat.format("message not found. derbyMessageId {0}", derbyMessageId));
        }
        if (!Objects.equals(record.getStatus(), MessageRecordEntity.Status.PENDING)) {
            throw new ServiceException("InvalidStatus", MessageFormat.format("Record invalid status. derbyMessageId {0}", record.getDerbyMessageId()));
        }
        if (MessageRecordEntity.MessageSource.CHANNEL.equals(record.getMessageSource())) {
            hotelSystemMessageService.sendMessage(record);
            messageRecordRepository.save(record);
        } else if (MessageRecordEntity.MessageSource.HOTEL_SYSTEM.equals(record.getMessageSource())) {
            // TODO
            log.warn("process hotel system message ignore");
        } else {
            throw new ServiceException("MessageSourceNotSupport", "messageSource not support");
        }
    }

    @Override
    public void parseAndProcessMessage(String derbyMessageId) {
        MessageRecordEntity message = messageRecordRepository.findByDerbyMessageId(derbyMessageId);
        if (Objects.isNull(message)) {
            throw new ServiceException("MessageNotFound", MessageFormat.format("message not found. derbyMessageId {0}", derbyMessageId));
        }
        var channelId = message.getChannelId();
        var messageContent = CompressUtil.decompressStr(message.getOriginMessage());
        channelMessageService.parseMessage(derbyMessageId, channelId, messageContent, message, true);
    }
}
