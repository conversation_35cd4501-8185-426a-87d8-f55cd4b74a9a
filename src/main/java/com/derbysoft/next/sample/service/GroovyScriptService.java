package com.derbysoft.next.sample.service;

import javax.script.*;
import java.net.MalformedURLException;
import java.util.Map;

/**
 * GroovyScriptService
 *
 * <AUTHOR> Yang
 */
public class GroovyScriptService implements ScriptService {

    @Override
    public Object execute(String script, Map<String, Object> properties) throws ScriptException, MalformedURLException {
        try {
            CompiledScript compiledScript = compile(script);

            SimpleBindings bindings = new SimpleBindings();
            bindings.putAll(properties);
            return compiledScript.eval(bindings);
        } catch (ScriptException e) {
            throw e;
        }
    }

    protected CompiledScript compile(String script) throws ScriptException, MalformedURLException {
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("groovy");
        return ((Compilable) engine).compile(script);
    }
}
