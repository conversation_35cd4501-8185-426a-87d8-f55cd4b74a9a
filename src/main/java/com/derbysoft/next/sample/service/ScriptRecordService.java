package com.derbysoft.next.sample.service;

import com.derbysoft.next.sample.domain.entity.ChannelScriptEntity;
import com.derbysoft.next.sample.domain.entity.HotelSystemScriptEntity;

public interface ScriptRecordService {

    ChannelScriptEntity loadChannelScript(String channelId, String scriptTag, String scriptVer);

    ChannelScriptEntity saveChannelScript(ChannelScriptEntity channelScript);

    HotelSystemScriptEntity loadHotelSystemScript(String hotelSystemId, String apiType, String hotelSystemVer, String scriptTag, String scriptVersion);

    HotelSystemScriptEntity saveHotelSystemScript(HotelSystemScriptEntity channelScript);
}
