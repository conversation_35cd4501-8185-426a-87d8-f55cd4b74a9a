package com.derbysoft.next.sample.exception;

import com.derbysoft.next.commons.boot.rest.ErrorResult;
import com.derbysoft.next.commons.core.exception.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * @author: cy.wang
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(value = {IllegalArgumentException.class})
    public ResponseEntity<ErrorResult> handleServiceException(IllegalArgumentException exception) {
        log.error(exception.getMessage(), exception);
        ErrorResult errorResult = new ErrorResult(ErrorCode.InvalidField.name(), exception.getMessage());
        return new ResponseEntity<>(errorR<PERSON><PERSON>, HttpStatus.BAD_REQUEST);
    }

}
