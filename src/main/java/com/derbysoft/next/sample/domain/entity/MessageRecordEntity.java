package com.derbysoft.next.sample.domain.entity;

import com.derbysoft.next.commons.hibernate.entity.AbstractEntity;
import com.derbysoft.next.sample.dto.MessageContent;
import com.derbysoft.next.sample.dto.ReservationDetail;
import com.derbysoft.next.sample.util.MessageContentConverter;
import com.derbysoft.next.sample.util.ReservationDetailConverter;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import jakarta.persistence.*;
import java.util.Date;

@Data
@Entity
@FieldNameConstants(innerTypeName = "CONSTANT")
@Table(name = "message_record", indexes = {
        @Index(name = "index_derby_message_id", columnList = "derby_message_id"),
        @Index(name = "index_channel_thread_message", columnList = "channel_id,thread_id,message_id")
})
public class MessageRecordEntity extends AbstractEntity {

    @Column(name = "channel_id")
    private String channelId;
    @Column(name = "thread_id")
    private String threadId;
    @Column(name = "message_id")
    private String messageId;
    @Column(name = "derby_message_id")
    private String derbyMessageId;
    @Column(name = "channel_hotel_id")
    private String channelHotelId;
    @Column(name = "hotel_id")
    private String hotelId;
    @Column(name = "hotel_system_connection_id")
    private String hotelSystemConnectionId;
    @Column(name = "hotel_system_id")
    private String hotelSystemId;
    @Column(name = "api_type")
    private String apiType;
    @Column(name = "hotel_system_ver")
    private String hotelSystemVer;
    @Column(name = "script_ver")
    private String scriptVer;
    @Column(name = "hotel_system_chain_id")
    private String hotelSystemChainId;
    @Column(name = "hotel_system_hotel_id")
    private String hotelSystemHotelId;
    @Column(name = "message_source")
    private String messageSource; // CHANNEL、HOTEL_SYSTEM
    @Column(name = "business_type")
    private String businessType; // RESERVATION、SUPPORT、OTHER
    @Column(name = "message_created_date")
    private Date messageCreatedDate;
    @Column(name = "message_type")
    private String messageType; // TEXT、IMAGE...
    @Column(name = "message_content", columnDefinition = "text")
    @Convert(converter = MessageContentConverter.class)
    private MessageContent messageContent;
    @Column(name = "error_message", columnDefinition = "text")
    private String errorMessage;
    @Column(name = "reservation_detail_json", columnDefinition = "text")
    @Convert(converter = ReservationDetailConverter.class)
    private ReservationDetail reservationDetail;
    @Column(name = "status")
    private String status; // PENDING、PROCESSING、SUCCESSFUL、FAILED、IGNORE
    @Column(name = "extensions_json", columnDefinition = "text")
    private String extensionsJson;
    @Column(name = "profile", columnDefinition = "text")
    private String profile;
    @Column(name = "origin_message", columnDefinition = "text")
    private String originMessage;

    public interface MessageSource {
        String CHANNEL = "CHANNEL";
        String HOTEL_SYSTEM = "HOTEL_SYSTEM";
    }

    public interface BusinessType {
        String RESERVATION = "RESERVATION";
        String SUPPORT = "SUPPORT";
        String OTHER = "OTHER";
    }

    public interface MessageType {
        String TEXT = "TEXT";
        String IMAGE = "IMAGE";
    }

    public interface Status {
        String PENDING = "Pending";
        String PROCESSING = "Processing";
        String SUCCESS = "Success";
        String FAILED = "Failed";
        String IGNORE = "Ignore";
    }
}