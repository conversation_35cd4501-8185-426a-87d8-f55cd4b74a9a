package com.derbysoft.next.sample.domain.repository;

import com.derbysoft.next.sample.domain.entity.MessageRecordEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface MessageRecordRepository extends JpaRepository<MessageRecordEntity, Long>, JpaSpecificationExecutor<MessageRecordEntity> {

    MessageRecordEntity findByChannelIdAndThreadIdAndMessageId(String channelId, String threadId, String messageId);

    MessageRecordEntity findByDerbyMessageId(String derbyMessageId);
}
