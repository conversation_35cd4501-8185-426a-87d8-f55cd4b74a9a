package com.derbysoft.next.sample.domain.entity;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;
import java.util.Set;

@Builder
@Getter
@Setter
@Document(collection = "emailRecord")
@CompoundIndexes({
        @CompoundIndex(name = "message_key_index", def = "{messageId:1,messageType:1}", unique = true)
})
public class EmailRecordEntity extends BaseEntity {

    @Id
    private String id;
    @Indexed(name = "message_group_index")
    private String messageGroupId;
    private String messageId;
    private String messageType;
    private String mailFrom;
    private Set<String> mailTo;
    private Set<String> mailCc;
    private Set<String> mailBcc;
    private String subject;
    private String content;
    private String template;
    private Map<String, Object> templateData;
    private String supplier;
    private String status;
    private DateTime sendTime;

    private Map<String, Object> extraInfo;

}
