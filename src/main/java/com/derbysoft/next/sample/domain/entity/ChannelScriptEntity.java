package com.derbysoft.next.sample.domain.entity;

import com.derbysoft.next.commons.hibernate.entity.AbstractEntity;
import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

@Data
@Entity
@Table(name = "channel_script",
        uniqueConstraints = @UniqueConstraint(name = "unique_index_channel_script",
                columnNames = {"channel_id", "script_tag"}))
public class ChannelScriptEntity extends AbstractEntity {

    @Column(name = "channel_id", nullable = false)
    private String channelId;
    @Column(name = "script_tag", nullable = false)
    private String scriptTag; // msgSend、 msgSend
    @Column(name = "script_version", nullable = false)
    private String scriptVersion;
    @Column(name = "script_content", columnDefinition = "longtext")
    private String scriptContent;

}