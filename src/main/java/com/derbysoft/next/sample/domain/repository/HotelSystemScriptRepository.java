package com.derbysoft.next.sample.domain.repository;

import com.derbysoft.next.sample.domain.entity.HotelSystemScriptEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface HotelSystemScriptRepository extends JpaRepository<HotelSystemScriptEntity, Long>, JpaSpecificationExecutor<HotelSystemScriptEntity> {

    HotelSystemScriptEntity findByHotelSystemIdAndApiTypeAndHotelSystemVerAndScriptTagAndScriptVersion(String hotelSystemId, String apiType, String hotelSystemVer, String scriptTag, String scriptVersion);
}
