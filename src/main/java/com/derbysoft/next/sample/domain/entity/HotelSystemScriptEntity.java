package com.derbysoft.next.sample.domain.entity;

import com.derbysoft.next.commons.hibernate.entity.AbstractEntity;
import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Data
@Entity
@Table(name = "hotel_system_script")
public class HotelSystemScriptEntity extends AbstractEntity {
    @Column(name = "hotel_system_id", nullable = false)
    private String hotelSystemId;
    @Column(name = "api_type", nullable = false)
    private String apiType;
    @Column(name = "hotel_system_ver", nullable = false)
    private String hotelSystemVer;
    @Column(name = "script_version", nullable = false)
    private String scriptVersion;
    @Column(name = "script_tag", nullable = false)
    private String scriptTag; // msgSend、 msgSend
    @Column(name = "script_content", columnDefinition = "longtext")
    private String scriptContent;
    @Column(name = "request_template", columnDefinition = "longtext")
    private String requestTemplate;

}