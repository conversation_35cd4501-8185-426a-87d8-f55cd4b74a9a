package com.derbysoft.next.sample.domain.repository;

import com.derbysoft.next.sample.domain.entity.ChannelScriptEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface ChannelScriptRepository extends JpaRepository<ChannelScriptEntity, Long>, JpaSpecificationExecutor<ChannelScriptEntity> {

    ChannelScriptEntity findByChannelIdAndScriptTagAndScriptVersion(String channelId, String scriptTag, String scriptVersion);
}
