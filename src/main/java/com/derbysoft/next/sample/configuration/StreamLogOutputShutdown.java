package com.derbysoft.next.sample.configuration;

import com.derbysoft.log.streamlog.output.LogOutput;
import com.derbysoft.next.commons.boot.web.context.NextSmartLifecycle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class StreamLogOutputShutdown implements NextSmartLifecycle {

    private final Logger logger = LoggerFactory.getLogger(StreamLogOutputShutdown.class);

    private volatile boolean running;

    @Override
    public void start() {
        this.running = true;
    }

    @Override
    public boolean isRunning() {
        return this.running;
    }

    @Override
    public void stop(Runnable callback) {
        this.running = false;
        (new Thread(() -> this.doShutdown(callback), "StreamLogOutputShutdown")).start();
    }

    private void doShutdown(Runnable callback) {
        logger.info("Shutdown StreamLogOutput ...");
        LogOutput.close();
        callback.run();
    }

    @Override
    public int getPhase() {
        return Integer.MAX_VALUE;
    }
}
