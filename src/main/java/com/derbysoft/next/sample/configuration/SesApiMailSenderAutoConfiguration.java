package com.derbysoft.next.sample.configuration;

import com.derbysoft.next.sample.mail.SesApiMailSender;
import com.derbysoft.next.sample.mail.SesMailProperties;
import com.derbysoft.next.sample.util.Constants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(prefix = "ses.mail.api", name = "region")
public class SesApiMailSenderAutoConfiguration {

    private final SesMailProperties properties;

    SesApiMailSenderAutoConfiguration(SesMailProperties properties) {
        this.properties = properties;
    }

    @Bean
    public SesApiMailSender sesApiMailSender() {
        SesApiMailSender sender = new SesApiMailSender();
        applyProperties(sender);
        return sender;
    }

    private void applyProperties(SesApiMailSender sender) {
        SesMailProperties.Api api = this.properties.getApi();
        sender.setRegion(api.getRegion());
        sender.setSenderName(Constants.MailSenderSupplier.SES_API);
        sender.setPriority(api.getPriority());
        String domainIdentity = api.getDomainIdentity();
        if (StringUtils.isNotBlank(domainIdentity)) {
            sender.setSource(domainIdentity);
        }
        String sourceArn = api.getSourceArn();
        if (StringUtils.isNotBlank(sourceArn)) {
            sender.setSourceArn(sourceArn);
        }
    }

}
