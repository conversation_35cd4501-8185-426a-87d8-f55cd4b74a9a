package com.derbysoft.next.sample.configuration;

import com.derbysoft.next.sample.mail.SesMailProperties;
import com.derbysoft.next.sample.mail.SesSmtpMailSender;
import com.derbysoft.next.sample.util.Constants;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(prefix = "ses.mail.smtp", name = "host")
public class SesSmtpMailSenderAutoConfiguration {

    private final SesMailProperties properties;

    SesSmtpMailSenderAutoConfiguration(SesMailProperties properties) {
        this.properties = properties;
    }

    @Bean
    public SesSmtpMailSender sesSmtpMailSender() {
        SesSmtpMailSender sender = new SesSmtpMailSender();
        applyProperties(sender);
        return sender;
    }

    private void applyProperties(SesSmtpMailSender sender) {
        SesMailProperties.Smtp smtp = this.properties.getSmtp();
        sender.setHost(smtp.getHost());
        sender.setPort(smtp.getPort());
        sender.setUsername(smtp.getUsername());
        sender.setPassword(smtp.getPassword());

        sender.setDomainIdentity(smtp.getDomainIdentity());
        sender.setSenderName(Constants.MailSenderSupplier.SES_SMTP);
        sender.setPriority(smtp.getPriority());
    }

}
