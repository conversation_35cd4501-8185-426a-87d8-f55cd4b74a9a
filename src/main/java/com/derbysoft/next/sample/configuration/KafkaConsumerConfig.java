package com.derbysoft.next.sample.configuration;

import lombok.Getter;
import lombok.Setter;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

@Configuration
@Getter
@Setter
public class KafkaConsumerConfig {

    private KafkaProperties kafkaProperties;

    @Bean("defaultContainerFactory")
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> defaultContainerFactory() {
        return containerFactory(properties -> {
        });
    }

    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> containerFactory(Consumer<Map<String, Object>> propertyConsumer) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();

        KafkaProperties.Consumer consumer = kafkaProperties.getConsumer();
        Map<String, Object> properties = new HashMap<>();
        properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, consumer.getBootstrapServers());
        properties.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, consumer.getEnableAutoCommit());
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, consumer.getGroupId());
        properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, consumer.getAutoOffsetReset());
        properties.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, consumer.getMaxPollRecords());
        properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, consumer.getKeyDeserializer());
        properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, consumer.getValueDeserializer());
        properties.putAll(consumer.getProperties());

        propertyConsumer.accept(properties);
        factory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(properties));

        KafkaProperties.Listener listener = kafkaProperties.getListener();
        factory.setConcurrency(listener.getConcurrency());
        ContainerProperties containerProperties = factory.getContainerProperties();
        containerProperties.setAckMode(listener.getAckMode());
        containerProperties.setPollTimeout(listener.getPollTimeout().toMillis());
        return factory;
    }


    @Autowired
    public void setKafkaProperties(KafkaProperties kafkaProperties) {
        this.kafkaProperties = kafkaProperties;
    }
}
