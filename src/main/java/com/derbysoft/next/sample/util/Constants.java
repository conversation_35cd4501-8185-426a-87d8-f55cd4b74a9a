package com.derbysoft.next.sample.util;

/**
 * <AUTHOR>
 */
public interface Constants {

    String STR_SUCCESSFUL = "Successful";

    String CONTACT_PERSON = "contactPerson";
    String GUESTS = "guests";

    interface MailTemplateType {
        String RESERVATION_STATUS = "ReservationStatus";
        String RESERVATION_STATUS_V2 = "ReservationStatusV2";
        // 20240306 add by yc 6033290326 start
        String CONNECTION_CHANGE = "ConnectionChange";
        // 20240306 add by yc 6033290326 end
        String INTERFACE_PASSWORD_RESET = "InterfacePasswordReset";
        String INTERFACE_PASSWORD_EXPIRY_WARNING = "InterfacePasswordExpiryWarning";
        String GHOST_BOOKING = "GhostBooking";

    }

    interface MailSenderSupplier {
        String GMAIL = "Gmail";
        String SEND_CLOUD = "SendCloud";
        String SES_API = "SesApi";
        String SES_SMTP = "SesSmtp";
        String UNKNOWN = "Unknown";
    }

    interface Kafka {
        String KAFKA_RAW_MAIL_MESSAGE_KEY = "RAW_MAIL:";
        String KAFKA_TEMPLATE_MAIL_MESSAGE_KEY = "TEMPLATE_MAIL:";
    }

    interface Perf {

        String PRODUCE_MESSAGE = "ProduceMessage";
        String CONSUME_MESSAGE = "ConsumeMessage";
        String IGNORE = "Ignore";

    }

    interface PerfField {

        String MESSAGE_TYPE = "message_type";
        String MESSAGE_TOPIC = "message_topic";
        String MESSAGE_KEY = "message_key";
        String MESSAGE_ID_DERBY = "message_id_derby";
        String MESSAGE_ID_CHANNEL = "message_id_channel";

        String HOTEL_DERBY = "hotel_derby";
        String CHANNEL = "channel";
        String SUPPLIER = "supplier";

        String CHILD_MESSAGE_ID = "ext_child_message_id";
        String MESSAGE_SIZE = "ext_message_size";
        String PARTITION = "ext_partition";
        String OFFSET = "ext_offset";
        String MESSAGE_TIMESTAMP = "ext_message_timestamp";

        String KAFKA_HEADERS = "kafka.headers";

        String EXT = "ext_";

    }

    interface ScriptTag {
        String PARSE = "msgParse";
        String SEND = "msgSend";
    }

    interface ScriptVersion {
        String DEFAULT_VERSION = "1.0";
        String MSG_PARSE_VER = "msgParseScriptVer";
        String MSG_SEND_VER = "msgSendScriptVer";
    }

    String CONTENT_TYPE_VALUE_JSON = "application/json; charset=utf-8";

}
