package com.derbysoft.next.sample.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Component
public class CommonUtils {

    public static String uuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 字符串化
     *
     * @param value
     * @return
     */
    public static String toString(Object value) {
        if (value == null) {
            return "";
        }

        if (value instanceof BigDecimal) {
            return ((BigDecimal) value).toPlainString();
        }
        return String.valueOf(value).trim();
    }

    // 20240524 add by andrew.yang 6593159538
    public static <T> List<List<T>> splitList(List<T> list, int size) {
        return IntStream.range(0, (list.size() + size - 1) / size)
                .mapToObj(i -> list.subList(i * size, Math.min((i + 1) * size, list.size())))
                .collect(Collectors.toList());
    }
}
