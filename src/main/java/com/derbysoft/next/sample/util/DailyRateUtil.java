package com.derbysoft.next.sample.util;

import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
public class DailyRateUtil {

    public static List<String> dailyRates(String checkin, String checkout, List<BigDecimal> hotelSystemAmountBeforeTax, List<BigDecimal> hotelSystemAmountAfterTax, String currency) {
        List<String> dailyRates = new ArrayList<>();
        try {
            LocalDate checkinDate = LocalDate.parse(checkin);
            LocalDate checkoutDate = LocalDate.parse(checkout);
            List<LocalDate> dateList;
            if (checkinDate.equals(checkoutDate)) {
                dateList = Collections.singletonList(checkinDate);
            } else {
                dateList = Stream.iterate(checkinDate, d -> d.plusDays(1))
                        .limit(Days.daysBetween(checkinDate, checkoutDate).getDays())
                        .collect(Collectors.toList());
            }
            List<BigDecimal> hotelSystemAmountTax = CollectionUtils.isEmpty(hotelSystemAmountBeforeTax) ?
                    hotelSystemAmountAfterTax : hotelSystemAmountBeforeTax;
            for (int index = 0; index < dateList.size(); index++) {
                StringBuilder sb = new StringBuilder();
                sb.append(dateList.get(index).toString("yyyy-MM-dd"));
                sb.append(": ");
                sb.append(hotelSystemAmountTax.get(index));
                sb.append(" ");
                sb.append(currency);
                dailyRates.add(sb.toString());
            }
        } catch (Exception e) {
            dailyRates.add("-");
        }
        return dailyRates;
    }
}
