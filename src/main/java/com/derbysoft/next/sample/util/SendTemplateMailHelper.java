package com.derbysoft.next.sample.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.ses.SesClient;
import software.amazon.awssdk.services.ses.model.*;

/**
 * <AUTHOR>
 */
public class SendTemplateMailHelper {

    private static final Logger log = LoggerFactory.getLogger(SendTemplateMailHelper.class);

    // aws From-Address
    private String fromAddress = "\"pc.noreply\" <<EMAIL>>";

    //aws From-Address-ARN
    private String fromAddressARN = "arn:aws:ses:us-west-2:325824670116:identity/aws-ses.derbysoft.com";

    public boolean sendTemplateMail(String[] sendTo, String[] bccTo, String subject, String emailContent) {
        if (sendTo == null || sendTo.length == 0) {
            log.error("");
            return false;
        }

        SesClient client = SesClient.builder()
                .region(Region.US_WEST_2)
                .build();
        log.info("client init successful");

        return sendMail(client, fromAddress, fromAddressARN, emailContent, sendTo, bccTo, subject);
    }

    private boolean sendMail(SesClient client, String source, String sourceArn,
                             String emailContent, String[] sendTo, String[] bccTo,
                             String subject) {
        if (sendTo == null || sendTo.length == 0) {
            return true;
        }

        // delete by andrew.yang use aws ses sdk 2.0 replace 1.0
        Destination.Builder builder = Destination.builder()
                .toAddresses(sendTo);
        if (bccTo != null && bccTo.length != 0) {
            builder.bccAddresses(bccTo);
        }
        SendEmailRequest request = SendEmailRequest.builder()
                .destination(builder.build())
                .message(
                        Message.builder()
                                .subject(Content.builder().charset("UTF-8").data(subject).build())
                                .body(Body.builder().html(Content.builder().charset("UTF-8").data(emailContent).build()).build())
                                .build()
                )
                .source(source)
                .sourceArn(sourceArn)
                .build();

        // delete by andrew.yang use aws ses sdk 2.0 replace 1.0
//        Destination destination = new Destination();
//        destination.withToAddresses(sendTo);
//        if (bccTo != null && bccTo.length != 0) {
//            destination.withBccAddresses(bccTo);
//        }
//        SendEmailRequest request = new SendEmailRequest()
//                .withDestination(destination)
//                .withMessage(new Message()
//                        .withBody(new Body()
//                        .withHtml(new Content()
//                                .withCharset("UTF-8").withData(emailContent)))
//                        .withSubject(new Content()
//                                .withCharset("UTF-8").withData(subject)))
//                .withSource(source)
//                .withSourceArn(sourceArn);
//        SendEmailResult result = client.sendEmail(request);

        SendEmailResponse response = client.sendEmail(request);
        log.info("SendMail SES, messageID={}", response.messageId());
        return true;
    }
}