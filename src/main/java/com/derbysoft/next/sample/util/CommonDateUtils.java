package com.derbysoft.next.sample.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
public class CommonDateUtils {
    public static final String DATETIME_PATTERN_UTC = "yyyy-MM-dd'T'HH:mm:ss'Z'";

    public static Date parseDate(String strDate, String pattern) {
        if (StringUtils.trimToNull(strDate) == null) {
            return null;
        }

        DateTimeFormatter formatter = DateTimeFormat.forPattern(pattern);
        return formatter.parseDateTime(strDate).toDate();
    }
}
