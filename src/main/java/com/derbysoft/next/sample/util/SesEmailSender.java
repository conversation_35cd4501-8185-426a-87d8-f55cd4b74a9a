package com.derbysoft.next.sample.util;

import jakarta.mail.util.ByteArrayDataSource;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.ses.SesClient;
import software.amazon.awssdk.services.ses.model.RawMessage;
import software.amazon.awssdk.services.ses.model.SendRawEmailRequest;
import software.amazon.awssdk.services.ses.model.SendRawEmailResponse;

import jakarta.activation.DataHandler;
import jakarta.activation.DataSource;
import jakarta.mail.Message;
import jakarta.mail.Multipart;
import jakarta.mail.Session;
import jakarta.mail.internet.*;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Properties;

public class SesEmailSender {

    public SendRawEmailResponse sendEmail(
            String sourceEmail,
            String sourceArn,
            String region,
            String[] to,
            String[] mailCc,
            String[] mailBcc,
            String subject,
            String emailContent,
            List<MultipartFile> attachments
    ) throws Exception {
        Session session = Session.getDefaultInstance(new Properties());
        MimeMessage message = new MimeMessage(session);

        // 设置 friendly name 可选
        message.setFrom(new InternetAddress(sourceEmail));

        setRecipients(message, Message.RecipientType.TO, to);
        setRecipients(message, Message.RecipientType.CC, mailCc);
        setRecipients(message, Message.RecipientType.BCC, mailBcc);

        message.setSubject(subject, StandardCharsets.UTF_8.name());

        Multipart multipart = new MimeMultipart();

        MimeBodyPart htmlPart = new MimeBodyPart();
        htmlPart.setContent(emailContent, "text/html; charset=UTF-8");
        multipart.addBodyPart(htmlPart);

        if (attachments != null) {
            for (MultipartFile file : attachments) {
                if (file != null && !file.isEmpty()) {
                    MimeBodyPart attachmentPart = createAttachmentPart(file);
                    multipart.addBodyPart(attachmentPart);
                }
            }
        }

        message.setContent(multipart);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        message.writeTo(outputStream);

        RawMessage rawMessage = RawMessage.builder()
                .data(SdkBytes.fromByteArray(outputStream.toByteArray()))
                .build();

        SendRawEmailRequest rawEmailRequest = SendRawEmailRequest.builder()
                .rawMessage(rawMessage)
                .source(sourceEmail)
                .fromArn(sourceArn)
                .sourceArn(sourceArn)
                .build();
        SesClient sesClient = SesClient.builder()
                .region(Region.of(region))
                .build();
        return sesClient.sendRawEmail(rawEmailRequest);
    }

    private void setRecipients(MimeMessage message, Message.RecipientType type, String[] addresses) throws Exception {
        if (addresses != null && addresses.length > 0) {
            InternetAddress[] internetAddresses = new InternetAddress[addresses.length];
            for (int i = 0; i < addresses.length; i++) {
                internetAddresses[i] = new InternetAddress(addresses[i]);
            }
            message.setRecipients(type, internetAddresses);
        }
    }

    private MimeBodyPart createAttachmentPart(MultipartFile file) throws Exception {
        MimeBodyPart attachmentPart = new MimeBodyPart();
        DataSource source = new ByteArrayDataSource(file.getBytes(), file.getContentType());
        attachmentPart.setDataHandler(new DataHandler(source));
        attachmentPart.setFileName(MimeUtility.encodeText(file.getOriginalFilename(), "UTF-8", null));
        return attachmentPart;
    }
}
