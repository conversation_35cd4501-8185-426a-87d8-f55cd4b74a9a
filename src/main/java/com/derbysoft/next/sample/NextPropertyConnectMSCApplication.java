package com.derbysoft.next.sample;

import com.derbysoft.next.commons.boot.configuration.ApplicationKey;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.mongodb.config.EnableMongoAuditing;

@EnableMongoAuditing
@EnableJpaRepositories
@EnableFeignClients
@ApplicationKey("pcmsc.config")
@EnableDiscoveryClient
@SpringBootApplication
public class NextPropertyConnectMSCApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(NextPropertyConnectMSCApplication.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(NextPropertyConnectMSCApplication.class);
    }

}