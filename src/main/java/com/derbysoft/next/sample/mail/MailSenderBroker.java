package com.derbysoft.next.sample.mail;

import com.derbysoft.next.commons.core.exception.ServiceException;
import com.derbysoft.next.sample.dto.SendEmailMessage;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.CharEncoding;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class MailSenderBroker {

    private static final Logger logger = LoggerFactory.getLogger(MailSenderBroker.class);

    private static final Integer DEFAULT_PHASE = 0;

    private final Map<String, MailSender> mailSenderMap = Maps.newHashMap();
    private final List<String> senderKeys = Lists.newArrayList();

    public MailSenderBroker(@Autowired List<MailSender> senders) {
        List<MailSender> mailSenders = Optional.ofNullable(senders).orElse(Lists.newArrayList());
        mailSenders.forEach(sender -> mailSenderMap.put(sender.getName(), sender));
        sortSender();
    }

    public String sendMail(SendEmailMessage message) throws Exception {
        return this.sendMail(message, DEFAULT_PHASE);
    }

    private String sendMail(SendEmailMessage message, Integer phase) throws Exception {
        if (phase >= senderKeys.size()) {
            throw new ServiceException("SendEmailError", "Send email error");
        }

        MailSender mailSender = getMailSender(phase);
        if (mailSender == null) {
            throw new ServiceException("MailSenderNotFound", "mail sender not found.");
        }

        try {
            if (logger.isDebugEnabled()) {
                logger.debug("use sender {} to send", mailSender.getName());
            }
            if (mailSender instanceof SmtpMailSender) {
                SmtpMailSender smtpMailSender = (SmtpMailSender) mailSender;
                MimeMessage mimeMessage = this.buildMimeMessage(message, smtpMailSender);
                smtpMailSender.send(mimeMessage);
            } else if (mailSender instanceof ApiMailSender) {
                ApiMailSender apiMailSender = (ApiMailSender) mailSender;
                apiMailSender.send(message.getMailTo(), message.getMailCc(), message.getMailBcc(),
                        message.getMailSubject(), message.getMailContent(), message.getAttachments());
            } else {
                throw new ServiceException("Unidentified", "mailSender unidentified");
            }
        } catch (Exception e) {
            phase += 1;
            if (phase >= senderKeys.size()) {
                logger.error("Email {} retry exceeds threshold.", message.getMessageId());
                throw new ServiceException("RetryExceedsThreshold", "Send email retry exceeds threshold.");
            } else {
                logger.error("Email {} via Sender {} send failed, errorMessage: {}, will retry or by other sender",
                        message.getMessageId(),
                        mailSender.getName(),
                        e.getMessage());
                return sendMail(message, phase);
            }
        }
        return mailSender.getName();
    }

    private MimeMessage buildMimeMessage(SendEmailMessage message, SmtpMailSender mailSender) throws MessagingException {
        String domainIdentity = mailSender.getDomainIdentity();
        String mailFrom = message.getMailFrom() + domainIdentity;

        MimeMessage mimeMessage = mailSender.createMimeMessage();
        mimeMessage.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_HTML_VALUE);
        MimeMessageHelper messageHelper = new MimeMessageHelper(mimeMessage, true, CharEncoding.UTF_8);
        messageHelper.setFrom(mailFrom);
        messageHelper.setTo(message.getMailTo());
        messageHelper.setCc(message.getMailCc());
        messageHelper.setBcc(message.getMailBcc());
        messageHelper.setSubject(message.getMailSubject());
        messageHelper.setText(message.getMailContent(), true);
        if (message.getAttachments() != null) {
            for (MultipartFile file : message.getAttachments()) {
                if (file != null && !file.isEmpty()) {
                    messageHelper.addAttachment(file.getOriginalFilename(), file);
                }
            }
        }
        return mimeMessage;
    }

    private MailSender getMailSender(Integer phase) {
        String senderKey = senderKeys.get(phase);
        return mailSenderMap.get(senderKey);
    }

    private void sortSender() {
        synchronized (this.senderKeys) {
            List<String> sortedSenderKeys = mailSenderMap.values()
                    .stream()
                    .sorted(Comparator.comparing(MailSender::getPriority))
                    .map(MailSender::getName)
                    .collect(Collectors.toList());
            this.senderKeys.clear();
            this.senderKeys.addAll(sortedSenderKeys);
        }
    }
}
