package com.derbysoft.next.sample.mail;

import com.derbysoft.next.sample.util.Constants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import jakarta.mail.internet.MimeMessage;

/**
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(prefix = "spring.mail", name = "host")
public class SpringMailSender implements SmtpMailSender {

    private Integer priority;
    private String domainIdentity;
    private String senderName;

    private JavaMailSender javaMailSender;

    @Override
    public String getDomainIdentity() {
        return this.domainIdentity;
    }

    @Override
    public String getName() {
        return this.senderName;
    }

    @Override
    public Integer getPriority() {
        return priority;
    }

    @Override
    public MimeMessage createMimeMessage() {
        return this.javaMailSender.createMimeMessage();
    }

    @Override
    public void send(MimeMessage message) throws Exception {
        this.javaMailSender.send(message);
    }

    @Resource
    public void setJavaMailSender(JavaMailSender javaMailSender) {
        this.javaMailSender = javaMailSender;
        this.setSenderName(Constants.MailSenderSupplier.UNKNOWN);
        if (javaMailSender instanceof JavaMailSenderImpl) {
            String host = ((JavaMailSenderImpl) javaMailSender).getHost();
            if ("smtp.gmail.com".equals(host)) {
                this.setSenderName(Constants.MailSenderSupplier.GMAIL);
            } else if ("smtp.sendcloud.net".equals(host)) {
                this.setSenderName(Constants.MailSenderSupplier.SEND_CLOUD);
            }
        }
    }

    @Value("${spring.mail.priority:1}")
    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    @Value("${spring.mail.domain-identity:@derbysoft.net}")
    public void setDomainIdentity(String domainIdentity) {
        this.domainIdentity = domainIdentity;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }
}
