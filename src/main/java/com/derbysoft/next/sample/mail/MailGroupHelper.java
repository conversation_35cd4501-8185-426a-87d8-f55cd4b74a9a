package com.derbysoft.next.sample.mail;

import com.derbysoft.next.sample.util.CommonUtils;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class MailGroupHelper {

    public enum RecipientType {TO, CC, BCC}

    private Integer mailMaxCount;

    @Data
    @Builder
    public static class MailGroup {
        private Set<String> mailTo;
        private Set<String> mailCc;
        private Set<String> mailBcc;

        public int totalCount() {
            return mailTo.size() + mailCc.size() + mailBcc.size();
        }
    }

    public List<MailGroup> groupWithDistribution(Set<String> mailTo,
                                                 Set<String> mailCc,
                                                 Set<String> mailBcc) {
        List<String> toList = toList(mailTo);
        List<String> ccList = toList(mailCc);
        List<String> bccList = toList(mailBcc);

        int totalCount = toList.size() + ccList.size() + bccList.size();
        int groupCount = (int) Math.ceil((double) totalCount / mailMaxCount);

        List<MailGroup> groups = new ArrayList<>();
        for (int i = 0; i < groupCount; i++) {
            groups.add(MailGroup.builder()
                    .mailTo(new HashSet<>())
                    .mailCc(new HashSet<>())
                    .mailBcc(new HashSet<>())
                    .build());
        }

        distribute(toList, groups, RecipientType.TO, mailMaxCount);
        distribute(ccList, groups, RecipientType.CC, mailMaxCount);
        distribute(bccList, groups, RecipientType.BCC, mailMaxCount);

        return groups;
    }

    private static void distribute(List<String> emails, List<MailGroup> groups, RecipientType type, int mailMaxCount) {
        int g = 0;
        for (String email : emails) {
            boolean added = false;
            int attempts = 0;

            while (!added && attempts < groups.size()) {
                MailGroup group = groups.get(g);
                if (group.totalCount() < mailMaxCount) {
                    switch (type) {
                        case TO:
                            group.mailTo.add(email);
                            break;
                        case CC:
                            group.mailCc.add(email);
                            break;
                        case BCC:
                            group.mailBcc.add(email);
                            break;
                    }
                    added = true;
                }
                g = (g + 1) % groups.size();
                attempts++;
            }

            if (!added) {
                // 如果所有组都满了，但还有剩余，新增一个组
                MailGroup newGroup = MailGroup.builder()
                        .mailTo(new HashSet<>())
                        .mailCc(new HashSet<>())
                        .mailBcc(new HashSet<>())
                        .build();
                switch (type) {
                    case TO:
                        newGroup.mailTo.add(email);
                        break;
                    case CC:
                        newGroup.mailCc.add(email);
                        break;
                    case BCC:
                        newGroup.mailBcc.add(email);
                        break;
                }
                groups.add(newGroup);
            }
        }
    }

    private List<String> toList(Set<String> set) {
        return Optional.ofNullable(set).map(ArrayList::new).orElseGet(ArrayList::new);
    }

    public List<MailGroup> groups(Set<String> mailTo, Set<String> mailCc) {
        ArrayList<String> mailToList = Lists.newArrayList(mailTo);
        ArrayList<String> mailCcList = Optional.ofNullable(mailCc).map(Lists::newArrayList).orElse(new ArrayList<>());

        if (mailToList.size() + mailCcList.size() <= this.mailMaxCount) {
            return Lists.newArrayList(MailGroup.builder().mailTo(mailTo).mailCc(mailCc).build());
        }

        int splitCount = mailMaxCount / 2;
        List<List<String>> mailToGroups = CommonUtils.splitList(mailToList, splitCount);
        List<List<String>> mailCcGroups = CommonUtils.splitList(mailCcList, splitCount);

        ArrayList<MailGroup> groups = Lists.newArrayList();
        if (mailToGroups.size() >= mailCcGroups.size()) {
            for (int index = 0; index < mailToGroups.size(); index++) {
                MailGroup.MailGroupBuilder mailGroupBuilder = MailGroup.builder()
                        .mailTo(Sets.newHashSet(mailToGroups.get(index)));
                if (mailCcGroups.size() > index) {
                    mailGroupBuilder.mailCc(Sets.newHashSet(mailCcGroups.get(index)));
                }
                groups.add(mailGroupBuilder.build());
            }
        } else {
            for (int index = 0; index < mailCcGroups.size(); index++) {
                List<String> mailTos = new ArrayList<>();
                List<String> mailCcs = Lists.newArrayList(mailCcGroups.get(index));
                if (mailToGroups.size() <= index) {
                    // move cclist 1st index to tolist
                    String move2Tos = mailCcs.remove(0);
                    mailTos.add(move2Tos);
                } else {
                    mailTos = mailToGroups.get(index);
                    mailCcs = mailCcGroups.get(index);
                }

                MailGroup.MailGroupBuilder mailGroupBuilder = MailGroup.builder()
                        .mailTo(Sets.newHashSet(mailTos));
                if (CollectionUtils.isNotEmpty(mailCcs)) {
                    mailGroupBuilder.mailCc(Sets.newHashSet(mailCcs));
                }
                groups.add(mailGroupBuilder.build());
            }
        }
        return groups;
    }

    @Value("${mail.max.count:40}")
    public void setMailMaxCount(Integer mailMaxCount) {
        this.mailMaxCount = mailMaxCount;
    }
}
