package com.derbysoft.next.sample.mail;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.ses.SesClient;
import software.amazon.awssdk.services.ses.model.*;

import jakarta.activation.DataHandler;
import jakarta.activation.DataSource;
import jakarta.mail.MessagingException;
import jakarta.mail.Multipart;
import jakarta.mail.Session;
import jakarta.mail.internet.*;
import jakarta.mail.util.ByteArrayDataSource;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

public class SesApiMailSender implements ApiMailSender {

    private static final Logger logger = LoggerFactory.getLogger(SesApiMailSender.class);

    private SesClient sesClient;

//    private static final String source = "\"pc.noreply\" <<EMAIL>>";
//    private static final String sourceArn = "arn:aws:ses:us-west-2:325824670116:identity/aws-ses.derbysoft.com";

    private String source = "\"pc.noreply\" <<EMAIL>>";
    private String sourceArn = "arn:aws:ses:us-west-2:325824670116:identity/aws-ses.derbysoft.com";

    private String senderName;
    private Integer priority;
    private String region;

    @Override
    public String getName() {
        return this.senderName;
    }

    @Override
    public Integer getPriority() {
        return this.priority;
    }

    @Override
    public void send(String[] to, String[] mailCc, String[] mailBcc, String subject, String emailContent, List<MultipartFile> attachments) throws Exception {
        // TODO 附件临时实现方案，后续可能根据正式方案进行替换
        if (CollectionUtils.isEmpty(attachments)) {
            this.sendEmail(to, mailCc, mailBcc, subject, emailContent);
        } else {
            this.sendEmail(to, mailCc, mailBcc, subject, emailContent, attachments);
        }
    }

    private void sendEmail(String[] to, String[] mailCc, String[] mailBcc, String subject, String emailContent, List<MultipartFile> attachments) throws Exception {
        SesClient client = this.getSesClient();

        Session session = Session.getDefaultInstance(new Properties());
        MimeMessage message = new MimeMessage(session);
        message.setFrom(new InternetAddress(this.source));

        // 设置收件人
        setRecipients(message, jakarta.mail.Message.RecipientType.TO, to);
        setRecipients(message, jakarta.mail.Message.RecipientType.CC, mailCc);
        setRecipients(message, jakarta.mail.Message.RecipientType.BCC, mailBcc);

        message.setSubject(subject, StandardCharsets.UTF_8.name());

        // 构建邮件内容（含 HTML 和附件）
        Multipart multipart = new MimeMultipart();

        // 正文部分
        MimeBodyPart htmlPart = new MimeBodyPart();
        htmlPart.setContent(emailContent, "text/html; charset=UTF-8");
        multipart.addBodyPart(htmlPart);

        // 附件部分
        if (attachments != null) {
            for (MultipartFile file : attachments) {
                if (file != null && !file.isEmpty()) {
                    MimeBodyPart attachmentPart = createAttachmentPart(file);
                    multipart.addBodyPart(attachmentPart);
                }
            }
        }

        message.setContent(multipart);

        // 写入 ByteArrayOutputStream 以构建 RawMessage
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        message.writeTo(outputStream);

        RawMessage rawMessage = RawMessage.builder()
                .data(SdkBytes.fromByteArray(outputStream.toByteArray()))
                .build();

        SendRawEmailRequest rawEmailRequest = SendRawEmailRequest.builder()
                .rawMessage(rawMessage)
                .source(this.source)
                .fromArn(this.sourceArn)
                .sourceArn(this.sourceArn)
                .build();
        client.sendRawEmail(rawEmailRequest);
    }

    private SendEmailResponse sendEmail(String[] to, String[] mailCc, String[] mailBcc, String subject, String emailContent) {
        // add by andrew.yang use aws ses sdk 2.0 replace 1.0
        try {
            SesClient client = this.getSesClient();
            SendEmailRequest request = SendEmailRequest.builder()
                    .destination(
                            Destination.builder()
                                    .toAddresses(to)
                                    .ccAddresses(mailCc)
                                    .bccAddresses(mailBcc)
                                    .build()
                    )
                    .message(
                            Message.builder()
                                    .subject(Content.builder().charset("UTF-8").data(subject).build())
                                    .body(Body.builder().html(Content.builder().charset("UTF-8").data(emailContent).build()).build())
                                    .build()
                    )
                    .source(this.source)
                    .sourceArn(this.sourceArn)
                    .build();
            return client.sendEmail(request);
        } catch (Exception e) {
            logger.error("The ses api email was not sent", e);
            throw e;
        }
        // delete by andrew.yang use aws ses sdk 2.0 replace 1.0
//        try {
//            AmazonSimpleEmailService client = this.getSesClient();
//            Destination destination = new Destination();
//            destination.withToAddresses(to).withBccAddresses(bcc);
//            SendEmailRequest request = new SendEmailRequest()
//                    .withDestination(destination)
//                    .withMessage(new Message()
//                            .withBody(new Body()
//                                    .withHtml(new Content()
//                                            .withCharset("UTF-8").withData(emailContent)))
//                            .withSubject(new Content()
//                                    .withCharset("UTF-8").withData(subject)))
//                    .withSource(source)
//                    .withSourceArn(sourceArn);
//            return client.sendEmail(request);
//        } catch (Exception e) {
//            logger.error("The ses api email was not sent", e);
//            throw e;
//        }
    }

    private void setRecipients(MimeMessage message, jakarta.mail.Message.RecipientType type, String[] addresses) throws MessagingException {
        if (addresses != null && addresses.length > 0) {
            InternetAddress[] internetAddresses = Arrays.stream(addresses)
                    .filter(addr -> addr != null && !addr.trim().isEmpty())
                    .map(addr -> {
                        try {
                            return new InternetAddress(addr.trim());
                        } catch (AddressException e) {
                            throw new RuntimeException("Invalid email address: " + addr, e);
                        }
                    })
                    .toArray(InternetAddress[]::new);
            message.setRecipients(type, internetAddresses);
        }
    }

    private MimeBodyPart createAttachmentPart(MultipartFile file) throws Exception {
        MimeBodyPart attachmentPart = new MimeBodyPart();
        String contentType = file.getContentType() != null ? file.getContentType() : "application/octet-stream";
        DataSource dataSource = new ByteArrayDataSource(file.getBytes(), contentType);
        attachmentPart.setDataHandler(new DataHandler(dataSource));
        attachmentPart.setFileName(MimeUtility.encodeText(file.getOriginalFilename(), "UTF-8", null));
        return attachmentPart;
    }

    private synchronized SesClient getSesClient() {
        if (this.sesClient == null) {
            this.sesClient = SesClient.builder()
                    .region(Region.of(this.region))
                    .build();
        }
        return this.sesClient;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public void setSourceArn(String sourceArn) {
        this.sourceArn = sourceArn;
    }
}
