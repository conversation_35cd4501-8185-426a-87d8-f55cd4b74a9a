package com.derbysoft.next.sample.mail;

import cn.hutool.core.io.resource.ResourceUtil;
import com.derbysoft.next.sample.dto.enums.Language;
import com.derbysoft.next.sample.dto.enums.MailTemplateType;
import com.derbysoft.next.sample.exception.TemplateNotFoundException;
import com.google.common.collect.Maps;
import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import groovy.lang.Script;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class DefaultMailTemplateBuilder implements MailTemplateBuilder {

    private final Map<String, Script> mailScriptMap = Maps.newHashMap();

    @PostConstruct
    public void init() {
        GroovyShell groovyShell = new GroovyShell(new Binding());
        register(MailTemplateType.ReservationStatus.name(),
                groovyShell.parse(ResourceUtil.readUtf8Str("templates/ReservationStatusTemplate.groovy")));
        register(MailTemplateType.ReservationStatusV2.name(),
                groovyShell.parse(ResourceUtil.readUtf8Str("templates/ReservationStatusTemplate_V2.groovy")));
        // 20240306 add by yc 6033290326 start
        register(MailTemplateType.ConnectionChange.name(),
                groovyShell.parse(ResourceUtil.readUtf8Str("templates/PropertyConnectionsUpdatesTemplate.groovy")));
        // 20240306 add by yc 6033290326 end
        register(MailTemplateType.InterfacePasswordReset.name(),
                groovyShell.parse(ResourceUtil.readUtf8Str("templates/OpenAPIInterfacePasswordResetTemplate.groovy")));
        register(MailTemplateType.InterfacePasswordExpiryWarning.name(),
                groovyShell.parse(ResourceUtil.readUtf8Str("templates/OpenAPIInterfacePasswordExpiryWarningTemplate.groovy")));
        register(MailTemplateType.GhostBooking.name(),
                groovyShell.parse(ResourceUtil.readUtf8Str("templates/GhostBookingDetectedTemplate.groovy")));
        register(MailTemplateType.HotelChannelOnboarding.name(),
                groovyShell.parse(ResourceUtil.readUtf8Str("templates/HotelChannelOnboardingTemplate.groovy")));
        register(MailTemplateType.NonPCChannelOnboarding.name(),
                groovyShell.parse(ResourceUtil.readUtf8Str("templates/NewPropertyOnboardingToNonPCChannelTemplate.groovy")));
        register(MailTemplateType.PropertyActivation.name(),
                groovyShell.parse(ResourceUtil.readUtf8Str("templates/PropertyActivation.groovy")));
        register(MailTemplateType.PropertyChannelActivation.name(),
                groovyShell.parse(ResourceUtil.readUtf8Str("templates/PropertyChannelActivation.groovy")));
    }

    private void register(String type, Script script) {
        mailScriptMap.put(type, script);
    }

    @Override
    public String build(String template, Map<String, Object> variables, Language language) {
        Script script = mailScriptMap.get(template);
        if (script == null) {
            throw new TemplateNotFoundException(template);
        }
        return Objects.toString(script.invokeMethod("fillingTemplate_" + language.name(), variables));
    }

}
