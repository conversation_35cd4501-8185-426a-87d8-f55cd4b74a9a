package com.derbysoft.next.sample.mail;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "ses.mail")
@Component
public class SesMailProperties {

    private SesMailProperties.Api api;
    private SesMailProperties.Smtp smtp;

    public Api getApi() {
        return api;
    }

    public void setApi(Api api) {
        this.api = api;
    }

    public Smtp getSmtp() {
        return smtp;
    }

    public void setSmtp(Smtp smtp) {
        this.smtp = smtp;
    }

    public static class Api {

        /**
         * Default value for priority.
         */
        public static final int DEFAULT_PRIORITY = 2;

        private String region;
        private Integer priority = DEFAULT_PRIORITY;
        private String domainIdentity;
        private String sourceArn;

        public String getRegion() {
            return region;
        }

        public void setRegion(String region) {
            this.region = region;
        }

        public Integer getPriority() {
            return priority;
        }

        public void setPriority(Integer priority) {
            this.priority = priority;
        }

        public String getDomainIdentity() {
            return domainIdentity;
        }

        public void setDomainIdentity(String domainIdentity) {
            this.domainIdentity = domainIdentity;
        }

        public String getSourceArn() {
            return sourceArn;
        }

        public void setSourceArn(String sourceArn) {
            this.sourceArn = sourceArn;
        }
    }

    public static class Smtp {

        /**
         * Default value for priority.
         */
        public static final int DEFAULT_PRIORITY = 3;

        private Integer priority = DEFAULT_PRIORITY;
        private String host;
        private Integer port;
        private String username;
        private String password;
        private String domainIdentity;

        public Integer getPriority() {
            return priority;
        }

        public void setPriority(Integer priority) {
            this.priority = priority;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public Integer getPort() {
            return port;
        }

        public void setPort(Integer port) {
            this.port = port;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getDomainIdentity() {
            return domainIdentity;
        }

        public void setDomainIdentity(String domainIdentity) {
            this.domainIdentity = domainIdentity;
        }
    }
}
