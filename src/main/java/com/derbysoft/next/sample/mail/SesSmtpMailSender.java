package com.derbysoft.next.sample.mail;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mail.MailSendException;

import jakarta.mail.Address;
import jakarta.mail.MessagingException;
import jakarta.mail.Session;
import jakarta.mail.Transport;
import jakarta.mail.internet.MimeMessage;
import java.util.Properties;

public class SesSmtpMailSender implements SmtpMailSender {

    private static final Logger logger = LoggerFactory.getLogger(SesSmtpMailSender.class);

    private Session session;
    
    private String host;
    private int port;
    private String username;
    private String password;

    private String domainIdentity;
    private String senderName;
    private Integer priority;

    @Override
    public String getDomainIdentity() {
        return this.domainIdentity;
    }

    @Override
    public String getName() {
        return this.senderName;
    }

    @Override
    public Integer getPriority() {
        return this.priority;
    }

    @Override
    public MimeMessage createMimeMessage() {
        return new MimeMessage(this.getSession());
    }

    @Override
    public void send(MimeMessage message) throws Exception {
        Transport transport = null;
        try {
            transport = this.connectTransport();
            Address[] addresses = message.getAllRecipients();
            transport.sendMessage(message, (addresses != null ? addresses : new Address[0]));
        } catch (Exception e) {
            logger.error("Send ses smtp email error.", e);
            throw e;
        } finally {
            try {
                if (transport != null) {
                    transport.close();
                }
            } catch (Exception ex) {
                throw new MailSendException("Failed to close server connection after message sending", ex);
            }
        }
    }

    private Transport connectTransport() throws MessagingException {
        Transport transport = this.getSession().getTransport();
        transport.connect(this.getHost(), this.getPort(), this.getUsername(), this.getPassword());
        return transport;
    }

    private synchronized Session getSession() {
        if (this.session == null) {
            Properties properties = new Properties();
            properties.put("mail.transport.protocol", "smtp");
            properties.put("mail.smtp.port", this.getPort());
            properties.put("mail.smtp.starttls.enable", "true");
            properties.put("mail.smtp.auth", "true");
            this.session = Session.getInstance(properties);
        }
        return this.session;
    }


    public void setHost(String host) {
        this.host = host;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getHost() {
        return host;
    }

    public int getPort() {
        return port;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public void setDomainIdentity(String domainIdentity) {
        this.domainIdentity = domainIdentity;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }
}
